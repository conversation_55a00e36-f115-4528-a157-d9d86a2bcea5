#!/usr/bin/env node

import https from 'https';
import http from 'http';

const projectId = '4d450b50-49e1-48dc-a04e-4cf81aa7268e';

// 获取小时数据
function getHourlyData() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 5173,
            path: `/api/projects/${projectId}/hourly-data`,
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    if (result.success) {
                        resolve(result.data);
                    } else {
                        reject(new Error(result.message || '获取小时数据失败'));
                    }
                } catch (error) {
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.end();
    });
}

// 获取项目数据
function getProjectData() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 5173,
            path: '/api/projects',
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    if (result.success) {
                        const project = result.data.items.find(p => p.id === projectId);
                        if (project) {
                            resolve(project);
                        } else {
                            reject(new Error('项目不存在'));
                        }
                    } else {
                        reject(new Error(result.message || '获取项目数据失败'));
                    }
                } catch (error) {
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.end();
    });
}

// 更新项目数据
function updateProjectData(project) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 5173,
            path: `/api/projects/${projectId}`,
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    if (result.success) {
                        resolve(result.data);
                    } else {
                        reject(new Error(result.message || '更新项目数据失败'));
                    }
                } catch (error) {
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.write(JSON.stringify(project));
        req.end();
    });
}

// 计算储能收益
function calculateStorageBenefit(hourlyData) {
    let storageBenefit = 0;
    let chargeHours = 0;
    let dischargeHours = 0;
    let totalChargeAmount = 0;
    let totalDischargeAmount = 0;

    for (const hour of hourlyData) {
        let hourlyBenefit = 0;
        if (hour.storageCharge > 0) {
            // 充电时，收益为负（使用上网电价）
            hourlyBenefit = -hour.gridFeedInPrice * hour.storageCharge;
            chargeHours++;
            totalChargeAmount += hour.storageCharge;
        } else if (hour.storageCharge < 0) {
            // 放电时，收益为正（使用用电价格）
            hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
            dischargeHours++;
            totalDischargeAmount += Math.abs(hour.storageCharge);
        }
        storageBenefit += hourlyBenefit;
    }

    return {
        storageBenefit,
        chargeHours,
        dischargeHours,
        totalChargeAmount,
        totalDischargeAmount
    };
}

// 主函数
async function main() {
    try {
        console.log('=== 开始计算正确的储能收益 ===');

        // 获取小时数据
        console.log('获取小时数据...');
        const hourlyData = await getHourlyData();
        console.log(`获取到 ${hourlyData.length} 个小时数据点`);

        // 检查前几个小时数据
        console.log('前5个小时数据:');
        for (let i = 0; i < Math.min(5, hourlyData.length); i++) {
            const hour = hourlyData[i];
            console.log(`小时 ${i}: storageCharge=${hour.storageCharge}, electricityPrice=${hour.electricityPrice}, gridFeedInPrice=${hour.gridFeedInPrice}`);
        }

        // 检查有储能充放电的数据
        const storageHours = hourlyData.filter(h => h.storageCharge !== 0);
        console.log(`有储能充放电的小时数: ${storageHours.length}`);
        if (storageHours.length > 0) {
            console.log('前3个储能充放电小时:');
            for (let i = 0; i < Math.min(3, storageHours.length); i++) {
                const hour = storageHours[i];
                console.log(`月${hour.month}日${hour.day}时${hour.hour}: storageCharge=${hour.storageCharge}, electricityPrice=${hour.electricityPrice}, gridFeedInPrice=${hour.gridFeedInPrice}`);
            }
        }

        // 计算储能收益
        console.log('计算储能收益...');
        const result = calculateStorageBenefit(hourlyData);

        console.log('=== 计算结果 ===');
        console.log(`储能收益: ${result.storageBenefit.toFixed(2)} 日元`);
        console.log(`充电小时数: ${result.chargeHours}`);
        console.log(`放电小时数: ${result.dischargeHours}`);
        console.log(`总充电量: ${result.totalChargeAmount.toFixed(1)} kWh`);
        console.log(`总放电量: ${result.totalDischargeAmount.toFixed(1)} kWh`);

        // 获取项目数据
        console.log('获取项目数据...');
        const project = await getProjectData();
        const currentStorageBenefit = project.analysisResults.yearlyData.storageBenefit;
        console.log(`当前储能收益: ${currentStorageBenefit} 日元`);

        // 更新项目数据
        console.log('更新项目数据...');
        project.analysisResults.yearlyData.storageBenefit = result.storageBenefit;
        project.analysisResults.yearlyData.totalBenefit =
            project.analysisResults.yearlyData.pvBenefit + result.storageBenefit;
        project.analysisResults.yearlyData.renewableEnergyBenefit =
            project.analysisResults.yearlyData.pvBenefit + result.storageBenefit;

        // 更新时间戳
        project.updatedAt = new Date().toISOString();
        project.analysisResults.analysisDate = new Date().toISOString();

        const updatedProject = await updateProjectData(project);
        console.log('项目数据更新成功');

        console.log('=== 修复完成 ===');
        console.log(`修复前储能收益: ${currentStorageBenefit} 日元`);
        console.log(`修复后储能收益: ${result.storageBenefit.toFixed(2)} 日元`);
        console.log(`差异: ${(result.storageBenefit - currentStorageBenefit).toFixed(2)} 日元`);

    } catch (error) {
        console.error('计算失败:', error.message);
        process.exit(1);
    }
}

// 运行主函数
main();
