/**
 * 投资成本计算工具
 * 统一计算项目投资成本，确保所有地方使用相同的计算逻辑
 */

import { ProjectData } from '../types/projectData';

/**
 * 投资成本明细接口
 */
export interface InvestmentBreakdown {
  pvModulesCost: number;
  energyStorageCost: number;
  invertersCost: number;
  otherInvestmentsCost: number;
  totalInvestment: number;
}

/**
 * 计算项目总投资成本
 * @param project 项目数据
 * @returns 投资成本明细
 */
export const calculateTotalInvestment = (project: ProjectData): InvestmentBreakdown => {
  // 计算光伏组件成本
  const pvModulesCost = project.pvModules.reduce((sum, module) => 
    sum + module.price * module.quantity, 0);

  // 计算储能设备成本
  const energyStorageCost = project.energyStorage.reduce((sum, storage) => 
    sum + storage.price * storage.quantity, 0);

  // 计算逆变器成本
  const invertersCost = project.inverters.reduce((sum, inverter) => 
    sum + inverter.price * inverter.quantity, 0);

  // 计算其他投资成本
  const otherInvestmentsCost = project.otherInvestments.reduce((sum, item) => 
    sum + item.price, 0);

  // 计算总投资成本
  const totalInvestment = pvModulesCost + energyStorageCost + invertersCost + otherInvestmentsCost;

  return {
    pvModulesCost,
    energyStorageCost,
    invertersCost,
    otherInvestmentsCost,
    totalInvestment
  };
};

/**
 * 计算选定光伏组件的投资成本
 * @param project 项目数据
 * @param selectedModuleIds 选定的组件ID数组
 * @returns 选定组件的投资成本明细
 */
export const calculateSelectedModulesInvestment = (
  project: ProjectData, 
  selectedModuleIds: string[]
): InvestmentBreakdown => {
  // 计算选中光伏组件成本
  const selectedModules = project.pvModules.filter(module => 
    selectedModuleIds.includes(module.id));
  const pvModulesCost = selectedModules.reduce((sum, module) => 
    sum + module.price * module.quantity, 0);

  // 计算所有光伏组件的总功率
  const totalPVPower = project.pvModules.reduce((sum, module) => 
    sum + module.power * module.quantity, 0);

  // 计算选中组件的总功率
  const selectedPVPower = selectedModules.reduce((sum, module) => 
    sum + module.power * module.quantity, 0);

  // 按功率比例分配其他投资成本
  const powerRatio = totalPVPower > 0 ? selectedPVPower / totalPVPower : 0;

  const energyStorageCost = project.energyStorage.reduce((sum, storage) => 
    sum + storage.price * storage.quantity, 0) * powerRatio;

  const invertersCost = project.inverters.reduce((sum, inverter) => 
    sum + inverter.price * inverter.quantity, 0) * powerRatio;

  const otherInvestmentsCost = project.otherInvestments.reduce((sum, item) => 
    sum + item.price, 0) * powerRatio;

  const totalInvestment = pvModulesCost + energyStorageCost + invertersCost + otherInvestmentsCost;

  return {
    pvModulesCost,
    energyStorageCost,
    invertersCost,
    otherInvestmentsCost,
    totalInvestment
  };
};

/**
 * 计算投资回报率
 * @param totalBenefit 年总收益
 * @param totalInvestment 总投资成本
 * @returns 投资回报率（百分比）
 */
export const calculateROI = (totalBenefit: number, totalInvestment: number): number => {
  return totalInvestment > 0 ? (totalBenefit / totalInvestment) * 100 : 0;
};

/**
 * 计算投资回收期
 * @param totalBenefit 年总收益
 * @param totalInvestment 总投资成本
 * @returns 投资回收期（年）
 */
export const calculatePaybackPeriod = (totalBenefit: number, totalInvestment: number): number => {
  return totalBenefit > 0 ? totalInvestment / totalBenefit : 0;
};
