/**
 * 数据一致性测试工具
 * 用于测试修复后的数据计算是否一致
 */

import { ProjectData } from '../types/projectData';
import { validateAnalysisResults } from './dataValidator';
import { calculateTotalInvestment } from './investmentCalculator';

/**
 * 测试项目数据一致性
 * @param project 项目数据
 * @returns 测试结果
 */
export const testDataConsistency = (project: ProjectData): {
  success: boolean;
  issues: string[];
  summary: string;
} => {
  const issues: string[] = [];

  try {
    // 1. 验证分析结果数据一致性
    const validationResult = validateAnalysisResults(project);
    if (!validationResult.isValid) {
      issues.push(...validationResult.errors.map(error => `数据验证错误: ${error}`));
    }
    if (validationResult.warnings.length > 0) {
      issues.push(...validationResult.warnings.map(warning => `数据验证警告: ${warning}`));
    }

    // 2. 测试投资成本计算一致性
    const investmentBreakdown = calculateTotalInvestment(project);
    const manualTotal = 
      project.pvModules.reduce((sum, module) => sum + module.price * module.quantity, 0) +
      project.energyStorage.reduce((sum, storage) => sum + storage.price * storage.quantity, 0) +
      project.inverters.reduce((sum, inverter) => sum + inverter.price * inverter.quantity, 0) +
      project.otherInvestments.reduce((sum, item) => sum + item.price, 0);

    if (Math.abs(investmentBreakdown.totalInvestment - manualTotal) > 0.01) {
      issues.push(`投资成本计算不一致: 工具函数=${investmentBreakdown.totalInvestment}, 手动计算=${manualTotal}`);
    }

    // 3. 测试年度数据引用一致性
    const yearlyData = project.analysisResults?.yearlyData;
    if (yearlyData) {
      // 检查总收益计算
      const calculatedTotalBenefit = yearlyData.pvBenefit + yearlyData.storageBenefit;
      if (Math.abs(calculatedTotalBenefit - yearlyData.totalBenefit) > 0.1) {
        issues.push(`总收益计算不一致: 计算值=${calculatedTotalBenefit}, 存储值=${yearlyData.totalBenefit}`);
      }

      // 检查ROI计算
      const expectedROI = investmentBreakdown.totalInvestment > 0 ? 
        (yearlyData.totalBenefit / investmentBreakdown.totalInvestment) * 100 : 0;
      if (Math.abs(expectedROI - yearlyData.roi) > 0.01) {
        issues.push(`ROI计算不一致: 期望=${expectedROI.toFixed(2)}%, 存储=${yearlyData.roi.toFixed(2)}%`);
      }

      // 检查回收期计算
      const expectedPaybackPeriod = yearlyData.totalBenefit > 0 ? 
        investmentBreakdown.totalInvestment / yearlyData.totalBenefit : 0;
      if (Math.abs(expectedPaybackPeriod - yearlyData.paybackPeriod) > 0.01) {
        issues.push(`回收期计算不一致: 期望=${expectedPaybackPeriod.toFixed(2)}年, 存储=${yearlyData.paybackPeriod.toFixed(2)}年`);
      }
    }

    // 4. 生成测试摘要
    const success = issues.length === 0;
    const summary = success ? 
      '✅ 数据一致性测试通过，所有计算结果一致' : 
      `❌ 发现${issues.length}个数据一致性问题`;

    return {
      success,
      issues,
      summary
    };

  } catch (error) {
    return {
      success: false,
      issues: [`测试执行失败: ${(error as Error).message}`],
      summary: '❌ 数据一致性测试执行失败'
    };
  }
};

/**
 * 批量测试多个项目的数据一致性
 * @param projects 项目数据数组
 * @returns 批量测试结果
 */
export const batchTestDataConsistency = (projects: ProjectData[]): {
  totalProjects: number;
  passedProjects: number;
  failedProjects: number;
  issues: { [projectId: string]: string[] };
  summary: string;
} => {
  const issues: { [projectId: string]: string[] } = {};
  let passedProjects = 0;
  let failedProjects = 0;

  projects.forEach(project => {
    const result = testDataConsistency(project);
    if (result.success) {
      passedProjects++;
    } else {
      failedProjects++;
      issues[project.id] = result.issues;
    }
  });

  const summary = `批量测试完成: ${projects.length}个项目, ${passedProjects}个通过, ${failedProjects}个失败`;

  return {
    totalProjects: projects.length,
    passedProjects,
    failedProjects,
    issues,
    summary
  };
};

/**
 * 生成数据一致性报告
 * @param project 项目数据
 * @returns 详细报告
 */
export const generateConsistencyReport = (project: ProjectData): string => {
  const result = testDataConsistency(project);
  
  let report = `# 数据一致性报告\n\n`;
  report += `**项目ID**: ${project.id}\n`;
  report += `**项目名称**: ${project.name}\n`;
  report += `**测试时间**: ${new Date().toLocaleString()}\n\n`;
  
  report += `## 测试结果\n\n`;
  report += `${result.summary}\n\n`;
  
  if (result.issues.length > 0) {
    report += `## 发现的问题\n\n`;
    result.issues.forEach((issue, index) => {
      report += `${index + 1}. ${issue}\n`;
    });
    report += `\n`;
  }
  
  // 添加数据摘要
  const yearlyData = project.analysisResults?.yearlyData;
  if (yearlyData) {
    report += `## 数据摘要\n\n`;
    report += `- **年发电量**: ${yearlyData.pvGeneration.toFixed(1)} kWh\n`;
    report += `- **年用电量**: ${yearlyData.electricityConsumption.toFixed(1)} kWh\n`;
    report += `- **光伏收益**: ¥${yearlyData.pvBenefit.toFixed(1)}\n`;
    report += `- **储能收益**: ¥${yearlyData.storageBenefit.toFixed(1)}\n`;
    report += `- **总收益**: ¥${yearlyData.totalBenefit.toFixed(1)}\n`;
    report += `- **投资回报率**: ${yearlyData.roi.toFixed(2)}%\n`;
    report += `- **投资回收期**: ${yearlyData.paybackPeriod.toFixed(2)}年\n`;
  }
  
  return report;
};
