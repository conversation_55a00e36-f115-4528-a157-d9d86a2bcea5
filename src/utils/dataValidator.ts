/**
 * 数据验证工具
 * 验证分析结果数据的一致性，确保数据计算正确
 */

import { ProjectData, ProjectAnalysisResults } from '../types/projectData';

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 验证分析结果数据的一致性
 * @param project 项目数据
 * @returns 验证结果
 */
export const validateAnalysisResults = (project: ProjectData): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  const analysisResults = project.analysisResults;
  if (!analysisResults) {
    errors.push('分析结果不存在');
    return { isValid: false, errors, warnings };
  }

  // 验证小时数据
  validateHourlyData(analysisResults, errors, warnings);

  // 验证年度数据一致性
  validateYearlyDataConsistency(analysisResults, errors, warnings);

  // 验证经济效益计算
  validateEconomicBenefits(analysisResults, errors, warnings);

  // 验证投资回报指标
  validateInvestmentReturns(project, analysisResults, errors, warnings);

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * 验证小时数据
 */
const validateHourlyData = (
  analysisResults: ProjectAnalysisResults,
  errors: string[],
  warnings: string[]
): void => {
  const { hourlyData } = analysisResults;

  if (!hourlyData || hourlyData.length !== 8760) {
    errors.push(`小时数据长度错误: 期望8760，实际${hourlyData?.length || 0}`);
    return;
  }

  // 检查数据完整性
  let invalidHours = 0;
  hourlyData.forEach((hour, index) => {
    if (!hour.pvGeneration || typeof hour.electricityConsumption !== 'number') {
      invalidHours++;
    }
  });

  if (invalidHours > 0) {
    warnings.push(`发现${invalidHours}个小时数据不完整`);
  }
};

/**
 * 验证年度数据一致性
 */
const validateYearlyDataConsistency = (
  analysisResults: ProjectAnalysisResults,
  errors: string[],
  warnings: string[]
): void => {
  const { hourlyData, yearlyData } = analysisResults;

  if (!yearlyData) {
    errors.push('年度数据不存在');
    return;
  }

  // 验证光伏发电量
  const calculatedPvGeneration = hourlyData.reduce((sum, hour) => {
    return sum + Object.values(hour.pvGeneration || {}).reduce((s, v) => s + (v || 0), 0);
  }, 0);

  const pvGenerationDiff = Math.abs(calculatedPvGeneration - yearlyData.pvGeneration);
  if (pvGenerationDiff > 0.1) {
    errors.push(`光伏发电量不一致: 计算值${calculatedPvGeneration.toFixed(3)}, 存储值${yearlyData.pvGeneration.toFixed(3)}`);
  }

  // 验证用电量
  const calculatedConsumption = hourlyData.reduce((sum, hour) => sum + hour.electricityConsumption, 0);
  const consumptionDiff = Math.abs(calculatedConsumption - yearlyData.electricityConsumption);
  if (consumptionDiff > 0.1) {
    errors.push(`用电量不一致: 计算值${calculatedConsumption.toFixed(3)}, 存储值${yearlyData.electricityConsumption.toFixed(3)}`);
  }

  // 验证电网交互
  const calculatedGridImport = hourlyData.reduce((sum, hour) => sum + hour.gridImport, 0);
  const gridImportDiff = Math.abs(calculatedGridImport - yearlyData.gridImport);
  if (gridImportDiff > 0.1) {
    errors.push(`电网购电量不一致: 计算值${calculatedGridImport.toFixed(3)}, 存储值${yearlyData.gridImport.toFixed(3)}`);
  }

  const calculatedGridExport = hourlyData.reduce((sum, hour) => sum + hour.gridExport, 0);
  const gridExportDiff = Math.abs(calculatedGridExport - yearlyData.gridExport);
  if (gridExportDiff > 0.1) {
    errors.push(`电网售电量不一致: 计算值${calculatedGridExport.toFixed(3)}, 存储值${yearlyData.gridExport.toFixed(3)}`);
  }
};

/**
 * 验证经济效益计算
 */
const validateEconomicBenefits = (
  analysisResults: ProjectAnalysisResults,
  errors: string[],
  warnings: string[]
): void => {
  const { hourlyData, yearlyData } = analysisResults;

  if (!yearlyData || !hourlyData) return;

  // 验证储能收益计算（使用与StorageAnalysisChart相同的逻辑）
  let calculatedStorageBenefit = 0;
  for (const hour of hourlyData) {
    let hourlyBenefit = 0;
    if (hour.storageCharge > 0) {
      // 充电时，收益为负（使用上网电价）
      hourlyBenefit = -hour.gridFeedInPrice * hour.storageCharge;
    } else if (hour.storageCharge < 0) {
      // 放电时，收益为正（使用用电价格）
      hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
    }
    calculatedStorageBenefit += hourlyBenefit;
  }

  const storageBenefitDiff = Math.abs(calculatedStorageBenefit - yearlyData.storageBenefit);
  if (storageBenefitDiff > 1.0) { // 允许1日元的误差
    errors.push(`储能收益计算不一致: 计算值${calculatedStorageBenefit.toFixed(1)}, 存储值${yearlyData.storageBenefit.toFixed(1)}`);
  }

  // 验证总收益计算
  const calculatedTotalBenefit = yearlyData.pvBenefit + yearlyData.storageBenefit;
  const totalBenefitDiff = Math.abs(calculatedTotalBenefit - yearlyData.totalBenefit);
  if (totalBenefitDiff > 0.1) {
    errors.push(`总收益计算错误: 光伏收益${yearlyData.pvBenefit} + 储能收益${yearlyData.storageBenefit} ≠ 总收益${yearlyData.totalBenefit}`);
  }

  // 验证新能源收益计算
  const renewableBenefitDiff = Math.abs(calculatedTotalBenefit - yearlyData.renewableEnergyBenefit);
  if (renewableBenefitDiff > 0.1) {
    errors.push(`新能源收益计算错误: 期望${calculatedTotalBenefit}, 实际${yearlyData.renewableEnergyBenefit}`);
  }

  // 检查收益合理性
  if (yearlyData.pvBenefit < 0) {
    warnings.push('光伏收益为负值，请检查计算逻辑');
  }

  if (yearlyData.totalBenefit < 0) {
    warnings.push('总收益为负值，请检查项目配置');
  }
};

/**
 * 验证投资回报指标
 */
const validateInvestmentReturns = (
  project: ProjectData,
  analysisResults: ProjectAnalysisResults,
  errors: string[],
  warnings: string[]
): void => {
  const { yearlyData } = analysisResults;

  if (!yearlyData) return;

  // 计算总投资成本进行验证
  const pvModulesCost = project.pvModules.reduce((sum, module) => sum + module.price * module.quantity, 0);
  const energyStorageCost = project.energyStorage.reduce((sum, storage) => sum + storage.price * storage.quantity, 0);
  const invertersCost = project.inverters.reduce((sum, inverter) => sum + inverter.price * inverter.quantity, 0);
  const otherInvestmentsCost = project.otherInvestments.reduce((sum, item) => sum + item.price, 0);
  const totalInvestment = pvModulesCost + energyStorageCost + invertersCost + otherInvestmentsCost;

  // 验证ROI计算
  const expectedROI = totalInvestment > 0 ? (yearlyData.totalBenefit / totalInvestment) * 100 : 0;
  const roiDiff = Math.abs(expectedROI - yearlyData.roi);
  if (roiDiff > 0.01) {
    errors.push(`ROI计算错误: 期望${expectedROI.toFixed(2)}%, 实际${yearlyData.roi.toFixed(2)}%`);
  }

  // 验证回收期计算
  const expectedPaybackPeriod = yearlyData.totalBenefit > 0 ? totalInvestment / yearlyData.totalBenefit : 0;
  const paybackDiff = Math.abs(expectedPaybackPeriod - yearlyData.paybackPeriod);
  if (paybackDiff > 0.01) {
    errors.push(`回收期计算错误: 期望${expectedPaybackPeriod.toFixed(2)}年, 实际${yearlyData.paybackPeriod.toFixed(2)}年`);
  }

  // 检查指标合理性
  if (yearlyData.roi > 100) {
    warnings.push(`ROI过高(${yearlyData.roi.toFixed(1)}%)，请检查收益计算`);
  }

  if (yearlyData.paybackPeriod > 50) {
    warnings.push(`回收期过长(${yearlyData.paybackPeriod.toFixed(1)}年)，请检查项目配置`);
  }
};

/**
 * 验证数据精度
 * @param value 数值
 * @param maxDecimals 最大小数位数
 * @returns 是否符合精度要求
 */
export const validatePrecision = (value: number, maxDecimals: number = 3): boolean => {
  const decimalPlaces = (value.toString().split('.')[1] || '').length;
  return decimalPlaces <= maxDecimals;
};
