import React from 'react';
import { Card } from 'antd';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import { ProjectData } from '../../types/projectData';

interface BenefitPieChartProps {
  project: ProjectData;
}

/**
 * 收益饼图组件
 */
const BenefitPieChart: React.FC<BenefitPieChartProps> = ({ project }) => {
  const { t } = useTranslation();

  // 获取年度收益数据
  const yearlyData = project.analysisResults?.yearlyData;
  if (!yearlyData) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          {t('analysis.noData')}
        </div>
      </Card>
    );
  }

  const pvBenefit = yearlyData.pvBenefit || 0;
  const storageBenefit = yearlyData.storageBenefit || 0;
  const gridExportIncome = yearlyData.gridExportIncome || 0;
  const totalBenefit = pvBenefit + storageBenefit;

  // 计算电费节省收益 = 光伏收益 - 余电上网收益
  const electricitySavings = pvBenefit - gridExportIncome;

  console.log('BenefitPieChart - 光伏收益(从yearlyData):', pvBenefit, '日元');
  console.log('BenefitPieChart - 储能收益(从yearlyData):', storageBenefit, '日元');
  console.log('BenefitPieChart - 余电上网收益:', gridExportIncome, '日元');
  console.log('BenefitPieChart - 电费节省收益:', electricitySavings, '日元');
  console.log('BenefitPieChart - 总收益:', totalBenefit, '日元');

  // 验证数据源一致性 - 使用与StorageAnalysisChart相同的计算方法
  const hourlyData = project.analysisResults?.hourlyData || [];
  let calculatedStorageBenefit = 0;
  for (const hour of hourlyData) {
    let hourlyBenefit = 0;
    if (hour.storageCharge > 0) {
      // 充电时，收益为负（使用上网电价）
      hourlyBenefit = -hour.gridFeedInPrice * hour.storageCharge;
    } else if (hour.storageCharge < 0) {
      // 放电时，收益为正（使用用电价格）
      hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
    }
    calculatedStorageBenefit += hourlyBenefit;
  }
  console.log('BenefitPieChart - 重新计算的储能收益:', calculatedStorageBenefit, '日元');

  const benefitDifference = Math.abs(storageBenefit - calculatedStorageBenefit);
  if (benefitDifference > 1.0) {
    console.error('❌ BenefitPieChart - 储能收益数据不一致!', {
      yearlyData: storageBenefit,
      calculated: calculatedStorageBenefit,
      difference: benefitDifference
    });
  } else {
    console.log('✅ BenefitPieChart - 储能收益数据一致');
  }

  // 准备外层饼图数据（总收益分解）
  const outerData = [
    {
      value: pvBenefit,
      name: t('analysis.pvBenefitShort'),
      itemStyle: { color: '#1890ff' }
    },
    {
      value: storageBenefit,
      name: t('analysis.storageBenefitShort'),
      itemStyle: { color: '#52c41a' }
    }
  ].filter(item => item.value > 0);

  // 准备内层饼图数据（只显示光伏收益分解）
  const innerData = [
    {
      value: electricitySavings,
      name: '电费节省收益',
      itemStyle: { color: '#faad14' }
    },
    {
      value: gridExportIncome,
      name: '余电上网收益',
      itemStyle: { color: '#ff4d4f' }
    }
  ].filter(item => item.value > 0);

  const option = {
    title: {
      text: t('analysis.benefitBreakdown'),
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        // 根据系列索引确定是外层还是内层
        if (params.seriesIndex === 0) {
          // 外层：总收益分解
          const percentage = totalBenefit > 0 ? ((params.value / totalBenefit) * 100).toFixed(1) : '0.0';
          return `${params.name}<br/>JPY ${params.value.toLocaleString()}<br/>${percentage}%`;
        } else {
          // 内层：光伏收益分解
          const percentage = pvBenefit > 0 ? ((params.value / pvBenefit) * 100).toFixed(1) : '0.0';
          return `${params.name}<br/>JPY ${params.value.toLocaleString()}<br/>${percentage}%`;
        }
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      data: [
        {
          name: t('analysis.pvBenefitShort'),
          icon: 'roundRect'
        },
        {
          name: '电费节省收益',
          icon: 'roundRect'
        },
        {
          name: '余电上网收益',
          icon: 'roundRect'
        },
        {
          name: t('analysis.storageBenefitShort'),
          icon: 'roundRect'
        }
      ],
      formatter: function(name: string) {
        // 光伏收益（主项）
        if (name === t('analysis.pvBenefitShort')) {
          const percentage = totalBenefit > 0 ? ((pvBenefit / totalBenefit) * 100).toFixed(1) : '0.0';
          return `${name} (${percentage}%)`;
        }

        // 储能收益（主项）
        if (name === t('analysis.storageBenefitShort')) {
          const percentage = totalBenefit > 0 ? ((storageBenefit / totalBenefit) * 100).toFixed(1) : '0.0';
          return `${name} (${percentage}%)`;
        }

        // 光伏收益子项（右移两个字符）
        if (name === '电费节省收益') {
          const percentage = pvBenefit > 0 ? ((electricitySavings / pvBenefit) * 100).toFixed(1) : '0.0';
          return `　　${name} (${percentage}%)`;
        }

        if (name === '余电上网收益') {
          const percentage = pvBenefit > 0 ? ((gridExportIncome / pvBenefit) * 100).toFixed(1) : '0.0';
          return `　　${name} (${percentage}%)`;
        }

        return name;
      }
    },
    series: [
      {
        name: '年收益饼图',
        type: 'pie',
        radius: ['50%', '80%'], // 外层饼图
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold',
            formatter: function(params: any) {
              const percentage = totalBenefit > 0 ? ((params.value / totalBenefit) * 100).toFixed(1) : '0.0';
              return `${params.name}\nJPY ${params.value.toLocaleString()}\n${percentage}%`;
            }
          }
        },
        labelLine: {
          show: false
        },
        data: outerData
      },
      {
        name: '光伏收益分解',
        type: 'pie',
        radius: ['20%', '50%'], // 内层饼图，与外层无缝隙
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold',
            formatter: function(params: any) {
              const percentage = pvBenefit > 0 ? ((params.value / pvBenefit) * 100).toFixed(1) : '0.0';
              return `${params.name}\nJPY ${params.value.toLocaleString()}\n${percentage}%`;
            }
          }
        },
        labelLine: {
          show: false
        },
        data: innerData
      }
    ]
  };

  return (
    <Card>
      <ReactECharts
        option={option}
        style={{ height: '400px', width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
    </Card>
  );
};

export default BenefitPieChart;
