import React from 'react';
import { Card } from 'antd';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import { ProjectData } from '../../types/projectData';

interface CashFlowChartProps {
  project: ProjectData;
}

/**
 * 现金流图组件
 */
const CashFlowChart: React.FC<CashFlowChartProps> = ({ project }) => {
  const { t } = useTranslation();

  // 获取年度数据
  const yearlyData = project.analysisResults?.yearlyData;
  if (!yearlyData) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          {t('analysis.noData')}
        </div>
      </Card>
    );
  }

  // 计算项目实施前的年购电费用
  // 正确计算：每小时用电量 × 本小时电价，然后8760小时累加
  const hourlyData = project.analysisResults?.hourlyData || [];
  let beforeProjectCost = 0;
  for (const hour of hourlyData) {
    const price = hour.electricityPrice || 25; // 使用小时数据中的电价
    beforeProjectCost += hour.electricityConsumption * price;
  }

  // 计算项目实施后的年购电费用（实际从电网购买的电量费用）
  const afterProjectCost = yearlyData.annualElectricityCost || 0;

  // 获取收益数据用于累减结构
  const pvBenefit = yearlyData.pvBenefit || 0;
  const storageBenefit = yearlyData.storageBenefit || 0;
  const gridExportIncome = yearlyData.gridExportIncome || 0;
  const electricitySavings = pvBenefit - gridExportIncome; // 电费节省收益

  console.log('CashFlowChart - 实施前年购电费用:', beforeProjectCost, '日元');
  console.log('CashFlowChart - 实施后年购电费用:', afterProjectCost, '日元');
  console.log('CashFlowChart - 差额:', beforeProjectCost - afterProjectCost, '日元');
  console.log('CashFlowChart - 新能源年收益:', pvBenefit + storageBenefit, '日元');
  console.log('CashFlowChart - 光伏收益:', pvBenefit, '日元');
  console.log('CashFlowChart - 储能收益:', storageBenefit, '日元');
  console.log('CashFlowChart - 余电上网收益:', gridExportIncome, '日元');
  console.log('CashFlowChart - 电费节省收益:', electricitySavings, '日元');

  // 准备图表数据
  const categories = [t('analysis.beforeProject'), t('analysis.afterProject')];

  // 实施前数据（单一柱状图）
  const beforeData = [beforeProjectCost, 0];

  // 实施后累减结构数据
  const afterElectricityCost = [0, afterProjectCost]; // 实施后电费（最底层）
  const storageReduction = [0, storageBenefit]; // 储能收益（第二层）
  const gridExportReduction = [0, gridExportIncome]; // 上网收益（第三层）
  const electricitySavingsReduction = [0, electricitySavings]; // 电费节省（第四层）

  const option = {
    title: {
      text: t('analysis.cashFlowChart'),
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        if (params[0].dataIndex === 0) {
          // 实施前
          return `${params[0].name}<br/>JPY ${beforeProjectCost.toLocaleString()}`;
        } else {
          // 实施后 - 显示各部分的详细信息
          let result = `${params[0].name}<br/>`;
          result += `实施后电费: JPY ${afterProjectCost.toLocaleString()}<br/>`;
          result += `储能收益: JPY ${storageBenefit.toLocaleString()}<br/>`;
          result += `上网收益: JPY ${gridExportIncome.toLocaleString()}<br/>`;
          result += `电费节省: JPY ${electricitySavings.toLocaleString()}<br/>`;
          result += `总节省: JPY ${(beforeProjectCost - afterProjectCost).toLocaleString()}`;
          return result;
        }
      }
    },
    legend: {
      data: ['实施前费用', '实施后电费', '储能收益', '上网收益', '电费节省'],
      top: 50
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        interval: 0,
        rotate: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '年费用 (JPY)',
      axisLabel: {
        formatter: function(value: number) {
          if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M';
          } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K';
          }
          return value.toString();
        }
      }
    },
    series: [
      {
        name: '实施前费用',
        type: 'bar',
        stack: 'total',
        data: beforeData,
        itemStyle: { color: '#f5222d' },
        label: {
          show: false
        }
      },
      {
        name: '实施后电费',
        type: 'bar',
        stack: 'total',
        data: afterElectricityCost,
        itemStyle: { color: '#52c41a' },
        label: {
          show: false
        }
      },
      {
        name: '储能收益',
        type: 'bar',
        stack: 'total',
        data: storageReduction,
        itemStyle: {
          color: '#ffffff',
          borderColor: '#722ed1',
          borderWidth: 2,
          borderType: 'solid'
        },
        label: {
          show: false
        }
      },
      {
        name: '上网收益',
        type: 'bar',
        stack: 'total',
        data: gridExportReduction,
        itemStyle: {
          color: '#ffffff',
          borderColor: '#1890ff',
          borderWidth: 2,
          borderType: 'solid'
        },
        label: {
          show: false
        }
      },
      {
        name: '电费节省',
        type: 'bar',
        stack: 'total',
        data: electricitySavingsReduction,
        itemStyle: {
          color: '#ffffff',
          borderColor: '#faad14',
          borderWidth: 2,
          borderType: 'solid'
        },
        label: {
          show: false
        }
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  };

  return (
    <Card>
      <ReactECharts
        option={option}
        style={{ height: '400px', width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
      <div style={{ textAlign: 'center', marginTop: '10px', color: '#666' }}>
        <div>
          年节省费用: JPY {(beforeProjectCost - afterProjectCost).toFixed(1)}
          ({(((beforeProjectCost - afterProjectCost) / beforeProjectCost) * 100).toFixed(1)}%)
        </div>
        <div style={{ fontSize: '12px', marginTop: '5px' }}>
          实施前: JPY {beforeProjectCost.toLocaleString()} |
          实施后: JPY {afterProjectCost.toLocaleString()} |
          新能源年收益: JPY {(pvBenefit + storageBenefit).toLocaleString()}
        </div>
      </div>
    </Card>
  );
};

export default CashFlowChart;
