import React from 'react';
import { Card } from 'antd';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import { ProjectData } from '../../types/projectData';

interface InvestmentPieChartProps {
  project: ProjectData;
}

/**
 * 投资饼图组件
 */
const InvestmentPieChart: React.FC<InvestmentPieChartProps> = ({ project }) => {
  const { t } = useTranslation();

  // 计算各项投资成本
  const pvModulesCost = project.pvModules.reduce((sum, module) =>
    sum + module.price * module.quantity, 0);
  const energyStorageCost = project.energyStorage.reduce((sum, storage) =>
    sum + storage.price * storage.quantity, 0);
  const invertersCost = project.inverters.reduce((sum, inverter) =>
    sum + inverter.price * inverter.quantity, 0);
  const otherInvestmentsCost = project.otherInvestments.reduce((sum, item) =>
    sum + item.price, 0);

  const totalInvestment = pvModulesCost + energyStorageCost + invertersCost + otherInvestmentsCost;

  // 准备饼图数据
  const investmentData = [
    {
      value: pvModulesCost,
      name: t('analysis.pvInvestment'),
      itemStyle: { color: '#1890ff' }
    },
    {
      value: energyStorageCost,
      name: t('analysis.storageInvestment'),
      itemStyle: { color: '#52c41a' }
    },
    {
      value: invertersCost,
      name: t('analysis.inverterInvestment'),
      itemStyle: { color: '#faad14' }
    },
    {
      value: otherInvestmentsCost,
      name: t('analysis.otherInvestment'),
      itemStyle: { color: '#f5222d' }
    }
  ].filter(item => item.value > 0); // 只显示有投资的项目

  const option = {
    title: {
      text: t('analysis.investmentBreakdown'),
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        const percentage = ((params.value / totalInvestment) * 100).toFixed(1);
        return `${params.name}<br/>JPY ${params.value.toLocaleString()}<br/>${percentage}%`;
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      formatter: function(name: string) {
        const item = investmentData.find(d => d.name === name);
        if (item) {
          const percentage = ((item.value / totalInvestment) * 100).toFixed(1);
          return `${name} (${percentage}%)`;
        }
        return name;
      }
    },
    series: [
      {
        name: t('analysis.investmentBreakdown'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
            formatter: function(params: any) {
              const percentage = ((params.value / totalInvestment) * 100).toFixed(1);
              return `${params.name}\nJPY ${params.value.toLocaleString()}\n${percentage}%`;
            }
          }
        },
        labelLine: {
          show: false
        },
        data: investmentData
      }
    ]
  };

  return (
    <Card>
      <ReactECharts
        option={option}
        style={{ height: '400px', width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
    </Card>
  );
};

export default InvestmentPieChart;
