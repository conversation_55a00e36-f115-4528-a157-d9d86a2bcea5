import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Table, Card, Checkbox, Button, Space, Row, Col, Select, Empty, Statistic, Divider, Modal, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, LineChartOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { PVModule } from '../../types/project';
import { ProjectData, ProjectHourlyData, ProjectMonthlyData } from '../../types/projectData';
import { formatCurrency, formatNumber } from '../../utils';
import PVGenerationChart from './PVGenerationChart';
import PVModuleModal from './PVModuleModal';
import StatisticWithTooltip from '../../components/common/StatisticWithTooltip';
import { calculateSelectedModulesInvestment } from '../../utils/investmentCalculator';
import PVTiltAnalysisChart from './PVTiltAnalysisChart';
import {
  performTiltAnalysis,
  TiltAnalysisData,
  saveTiltAnalysisToProject,
  getTiltAnalysisFromProject
} from '../../services/pvTiltAnalysisService';
import ProgressDialog from '../common/ProgressDialog';

const { Option } = Select;

interface PVAnalysisTabProps {
  project: ProjectData;
  onAddModule?: () => void;
  onEditModule?: (module: PVModule) => void;
  onDeleteModule?: (moduleId: string) => void;
  onProjectUpdate?: (updatedProject: ProjectData) => void;
}

interface PVVisualizationOptions {
  metric: 'generation' | 'income';
  viewMode: 'hourly' | 'daily' | 'monthly';
  month: number;
  day: number;
}

/**
 * 光伏分析标签页组件
 */
const PVAnalysisTab: React.FC<PVAnalysisTabProps> = ({
  project,
  onAddModule,
  onEditModule,
  onDeleteModule,
  onProjectUpdate
}) => {
  const { t } = useTranslation();
  const [selectedModules, setSelectedModules] = useState<string[]>([]);
  const [visualizationOptions, setVisualizationOptions] = useState<PVVisualizationOptions>({
    metric: 'generation',
    viewMode: 'monthly',
    month: 0, // 0表示全年
    day: 1
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [editingModule, setEditingModule] = useState<PVModule | null>(null);
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
  const [moduleToDelete, setModuleToDelete] = useState<string | null>(null);

  // 倾角分析相关状态
  const [tiltAnalysisData, setTiltAnalysisData] = useState<TiltAnalysisData | null>(null);
  const [tiltAnalysisLoading, setTiltAnalysisLoading] = useState(false);
  const [tiltAnalysisError, setTiltAnalysisError] = useState<string | null>(null);
  const [showTiltAnalysis, setShowTiltAnalysis] = useState(false);

  // 进度对话框状态
  const [progressDialogVisible, setProgressDialogVisible] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [analysisMessage, setAnalysisMessage] = useState('');
  const [analysisStarted, setAnalysisStarted] = useState(false);

  // 用于节流进度更新的ref
  const progressUpdateTimeoutRef = useRef<number | null>(null);

  // 节流的进度更新函数
  const throttledProgressUpdate = useCallback((progress: number, message: string) => {
    // 清除之前的定时器
    if (progressUpdateTimeoutRef.current) {
      clearTimeout(progressUpdateTimeoutRef.current);
    }

    // 设置新的定时器，延迟100ms更新
    progressUpdateTimeoutRef.current = window.setTimeout(() => {
      setAnalysisProgress(progress);
      setAnalysisMessage(message);
    }, 100);
  }, []);

  // 检查是否有已保存的倾角分析数据
  useEffect(() => {
    if (selectedModules.length > 0) {
      const savedAnalysis = getTiltAnalysisFromProject(project, selectedModules);
      if (savedAnalysis) {
        console.log('找到已保存的倾角分析数据:', savedAnalysis);
        const analysisData: TiltAnalysisData = {
          results: savedAnalysis.results,
          optimalTiltAngle: savedAnalysis.optimalTiltAngle,
          maxAnnualGeneration: savedAnalysis.maxAnnualGeneration,
          maxAnnualBenefit: savedAnalysis.maxAnnualBenefit
        };
        setTiltAnalysisData(analysisData);
        setShowTiltAnalysis(true);
      } else {
        setTiltAnalysisData(null);
        setShowTiltAnalysis(false);
      }
    } else {
      setTiltAnalysisData(null);
      setShowTiltAnalysis(false);
    }
  }, [selectedModules, project]);

  // 当项目变化时，重置选中的组件
  useEffect(() => {
    if (project.pvModules.length > 0) {
      // 默认选中第一个组件
      setSelectedModules([project.pvModules[0].id]);
    } else {
      setSelectedModules([]);
    }
  }, [project]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (progressUpdateTimeoutRef.current) {
        clearTimeout(progressUpdateTimeoutRef.current);
      }
    };
  }, []);

  // 处理组件选择变更
  const handleModuleSelectionChange = (moduleId: string, checked: boolean) => {
    if (checked) {
      setSelectedModules(prev => [...prev, moduleId]);
    } else {
      setSelectedModules(prev => prev.filter(id => id !== moduleId));
    }
  };

  // 处理可视化选项变更
  const handleVisualizationOptionsChange = (options: Partial<PVVisualizationOptions>) => {
    setVisualizationOptions(prev => ({ ...prev, ...options }));
  };

  // 获取选中组件的数据
  const getSelectedModulesData = () => {
    console.log('PVAnalysisTab - 开始获取选中组件数据');
    console.log('PVAnalysisTab - 选中的组件ID:', selectedModules);

    const selectedModulesList = project.pvModules.filter(module =>
      selectedModules.includes(module.id)
    );
    console.log('PVAnalysisTab - 选中的组件数量:', selectedModulesList.length);

    if (selectedModulesList.length === 0) {
      console.warn('PVAnalysisTab - 没有选中任何组件');
      return null;
    }

    // 获取小时数据
    const hourlyData = project.analysisResults?.hourlyData || [];
    console.log('PVAnalysisTab - 小时数据点数量:', hourlyData.length);

    // 检查hourlyData的结构
    if (hourlyData.length > 0) {
      console.log('PVAnalysisTab - 小时数据样本:', hourlyData[0]);
      console.log('PVAnalysisTab - pvGeneration类型:', typeof hourlyData[0].pvGeneration);
      console.log('PVAnalysisTab - pvGeneration值:', hourlyData[0].pvGeneration);
    } else {
      console.warn('PVAnalysisTab - 没有小时数据');
    }

    // 每月的天数（非闰年）
    const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    // 初始化月数据结构
    const monthlyData: any[] = [];
    for (let month = 1; month <= 12; month++) {
      monthlyData.push({
        month,
        pvGeneration: 0,
        pvBenefit: 0
      });
    }
    console.log('PVAnalysisTab - 初始化月数据结构，数量:', monthlyData.length);

    // 初始化日数据结构
    const dailyData: any[] = [];
    for (let month = 1; month <= 12; month++) {
      for (let day = 1; day <= daysInMonth[month - 1]; day++) {
        dailyData.push({
          day,
          month,
          pvGeneration: 0,
          pvBenefit: 0
        });
      }
    }
    console.log('PVAnalysisTab - 初始化日数据结构，数量:', dailyData.length);

    // 初始化小时数据结构
    const mergedHourlyData: any[] = [];
    for (let month = 1; month <= 12; month++) {
      for (let day = 1; day <= daysInMonth[month - 1]; day++) {
        for (let hour = 0; hour < 24; hour++) {
          mergedHourlyData.push({
            hour,
            day,
            month,
            pvGeneration: 0,
            pvBenefit: 0
          });
        }
      }
    }
    console.log('PVAnalysisTab - 初始化小时数据结构，数量:', mergedHourlyData.length);

    // 使用分析结果中的年度数据，而不是重新计算收益
    const yearlyData = project.analysisResults?.yearlyData;
    if (!yearlyData) {
      console.warn('PVAnalysisTab - 年度数据不存在');
      return null;
    }

    // 计算选中组件的总发电量
    let totalSelectedGeneration = 0;
    for (const hour of hourlyData) {
      for (const moduleId of selectedModules) {
        if (hour.pvGeneration && hour.pvGeneration[moduleId] !== undefined) {
          totalSelectedGeneration += hour.pvGeneration[moduleId] || 0;
        }
      }
    }

    // 按发电量比例分配光伏收益
    const totalPvGeneration = yearlyData.pvGeneration;
    const generationRatio = totalPvGeneration > 0 ? totalSelectedGeneration / totalPvGeneration : 0;
    const totalBenefit = yearlyData.pvBenefit * generationRatio;

    console.log('PVAnalysisTab - 选中组件总发电量:', totalSelectedGeneration, 'kWh');
    console.log('PVAnalysisTab - 总光伏发电量:', totalPvGeneration, 'kWh');
    console.log('PVAnalysisTab - 发电量比例:', generationRatio);
    console.log('PVAnalysisTab - 分配的光伏收益:', totalBenefit, 'JPY');

    // 处理每个小时数据，按比例分配
    for (const hour of hourlyData) {
      // 获取选中组件在当前小时的发电量
      let selectedModulesGeneration = 0;
      for (const moduleId of selectedModules) {
        if (hour.pvGeneration && hour.pvGeneration[moduleId] !== undefined) {
          selectedModulesGeneration += hour.pvGeneration[moduleId] || 0;
        }
      }

      // 按比例分配该小时的收益
      const hourBenefit = totalSelectedGeneration > 0 ?
        (selectedModulesGeneration / totalSelectedGeneration) * totalBenefit : 0;

      // 更新小时数据
      const hourIndex = mergedHourlyData.findIndex(h =>
        h.month === hour.month && h.day === hour.day && h.hour === hour.hour
      );
      if (hourIndex >= 0) {
        mergedHourlyData[hourIndex].pvGeneration = selectedModulesGeneration;
        mergedHourlyData[hourIndex].pvBenefit = hourBenefit;
      }

      // 更新日数据
      const dayIndex = dailyData.findIndex(d => d.month === hour.month && d.day === hour.day);
      if (dayIndex >= 0) {
        dailyData[dayIndex].pvGeneration += selectedModulesGeneration;
        dailyData[dayIndex].pvBenefit += hourBenefit;
      }

      // 更新月数据
      const monthIndex = hour.month - 1;
      if (monthIndex >= 0 && monthIndex < 12) {
        monthlyData[monthIndex].pvGeneration += selectedModulesGeneration;
        monthlyData[monthIndex].pvBenefit += hourBenefit;
      }
    }

    return {
      pvGeneration: totalSelectedGeneration,
      pvBenefit: totalBenefit,
      monthlyData,
      dailyData,
      hourlyData: mergedHourlyData
    };
  };

  // 处理添加组件
  const handleAddModule = () => {
    setEditingModule(null);
    setModalVisible(true);
  };

  // 处理编辑组件
  const handleEditModule = (module: PVModule) => {
    setEditingModule(module);
    setModalVisible(true);
  };

  // 处理删除组件
  const handleDeleteModule = (moduleId: string) => {
    setModuleToDelete(moduleId);
    setDeleteConfirmVisible(true);
  };

  // 确认删除组件
  const confirmDeleteModule = () => {
    if (moduleToDelete && onDeleteModule) {
      onDeleteModule(moduleToDelete);
      // 如果删除的组件在选中列表中，则从选中列表中移除
      if (selectedModules.includes(moduleToDelete)) {
        setSelectedModules(prev => prev.filter(id => id !== moduleToDelete));
      }
      message.success(t('pvModules.deleteSuccess'));
    }
    setDeleteConfirmVisible(false);
    setModuleToDelete(null);
  };

  // 保存组件
  const handleSaveModule = (module: PVModule) => {
    if (onEditModule) {
      onEditModule(module);
      message.success(editingModule ? t('pvModules.editSuccess') : t('pvModules.addSuccess'));
    }
    setModalVisible(false);
    setEditingModule(null);
  };

  // 处理倾角分析
  const handleTiltAnalysis = () => {
    if (selectedModules.length === 0) {
      message.warning(t('analysis.selectModulesForTiltAnalysis'));
      return;
    }

    // 显示进度对话框
    setProgressDialogVisible(true);
    setAnalysisProgress(0);
    setAnalysisMessage(t('analysis.preparingTiltAnalysis'));
    setAnalysisStarted(false);
  };

  // 确认开始倾角分析
  const confirmTiltAnalysis = async () => {
    try {
      setAnalysisStarted(true);
      setProgressDialogVisible(false); // 关闭对话框
      setTiltAnalysisLoading(true);
      setTiltAnalysisError(null);
      setShowTiltAnalysis(true);

      console.log('开始倾角分析，选中组件:', selectedModules);

      // 进度回调函数
      const progressCallback = (progress: number, message: string) => {
        console.log(`倾角分析进度: ${progress}% - ${message}`);
        throttledProgressUpdate(progress, message);
      };

      const analysisData = await performTiltAnalysis(project, selectedModules, progressCallback);

      // 保存分析结果到项目数据
      try {
        const updatedProject = await saveTiltAnalysisToProject(project, selectedModules, analysisData);
        console.log('倾角分析结果已保存到项目数据');

        // 通知父组件项目数据已更新
        if (onProjectUpdate) {
          onProjectUpdate(updatedProject);
        }
      } catch (saveError) {
        console.error('保存倾角分析结果失败:', saveError);
        message.warning('分析完成，但保存结果时出现问题');
      }

      setTiltAnalysisData(analysisData);
      message.success(t('analysis.tiltAnalysisComplete'));
    } catch (error) {
      console.error('倾角分析失败:', error);
      setTiltAnalysisError((error as Error).message || t('analysis.tiltAnalysisError'));
      message.error(t('analysis.tiltAnalysisError'));
    } finally {
      setTiltAnalysisLoading(false);
      setAnalysisProgress(0);
      setAnalysisMessage('');
    }
  };

  // 取消倾角分析
  const cancelTiltAnalysis = () => {
    setProgressDialogVisible(false);
    setAnalysisProgress(0);
    setAnalysisMessage('');
    setAnalysisStarted(false);
    if (tiltAnalysisLoading) {
      setTiltAnalysisLoading(false);
      message.info(t('analysis.tiltAnalysisCancelled'));
    }
  };

  // 渲染组件列表
  const renderModuleList = () => {
    const columns = [
      {
        title: '',
        key: 'selection',
        width: 50,
        render: (_: any, record: PVModule) => (
          <Checkbox
            checked={selectedModules.includes(record.id)}
            onChange={(e) => handleModuleSelectionChange(record.id, e.target.checked)}
          />
        ),
      },
      {
        title: t('pvModules.name'),
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: t('pvModules.manufacturer'),
        dataIndex: 'manufacturer',
        key: 'manufacturer',
      },
      {
        title: t('pvModules.model'),
        dataIndex: 'model',
        key: 'model',
      },
      {
        title: t('pvModules.power') + ' (W)',
        dataIndex: 'power',
        key: 'power',
      },
      {
        title: t('pvModules.efficiency') + ' (%)',
        dataIndex: 'efficiency',
        key: 'efficiency',
      },
      {
        title: t('pvModules.area') + ' (m²)',
        dataIndex: 'area',
        key: 'area',
        render: (value: number) => formatNumber(value, 1),
      },
      {
        title: t('pvModules.quantity'),
        dataIndex: 'quantity',
        key: 'quantity',
      },
      {
        title: t('pvModules.totalPower') + ' (kW)',
        key: 'totalPower',
        render: (_: any, record: PVModule) => formatNumber((record.power * record.quantity) / 1000, 1),
      },
      {
        title: t('pvModules.totalArea') + ' (m²)',
        key: 'totalArea',
        render: (_: any, record: PVModule) => formatNumber(record.area * record.quantity, 1),
      },
      {
        title: t('common.operation'),
        key: 'operation',
        width: 120,
        render: (_: any, record: PVModule) => (
          <Space size="small">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditModule(record)}
              title={t('common.edit')}
            />
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteModule(record.id)}
              title={t('common.delete')}
            />
          </Space>
        ),
      },
    ];

    return (
      <Card
        title={t('pvModules.moduleList')}
        extra={
          <Space>
            <Button
              type="default"
              icon={<LineChartOutlined />}
              onClick={handleTiltAnalysis}
              loading={tiltAnalysisLoading}
              disabled={selectedModules.length === 0}
            >
              {t('analysis.pvTiltAnalysis')}
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddModule}
            >
              {t('pvModules.addModule')}
            </Button>
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        <Table
          rowKey="id"
          columns={columns}
          dataSource={project.pvModules}
          pagination={false}
          size="small"
        />
      </Card>
    );
  };

  // 渲染可视化选项
  const renderVisualizationOptions = () => {
    const { metric, viewMode, month, day } = visualizationOptions;

    return (
      <div style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: '16px', marginBottom: 16 }}>
          {/* 数据指标 */}
          <div>
            <span style={{ marginRight: 8 }}>{t('chart.metric')}:</span>
            <Select
              value={metric}
              style={{ width: 120 }}
              onChange={(value) => handleVisualizationOptionsChange({ metric: value })}
            >
              <Option value="generation">{t('chart.generation')}</Option>
              <Option value="income">{t('chart.income')}</Option>
            </Select>
          </div>

          {/* 查看方式 */}
          <div>
            <span style={{ marginRight: 8 }}>{t('chart.viewMode')}:</span>
            <Select
              value={viewMode}
              style={{ width: 120 }}
              onChange={(value) => handleVisualizationOptionsChange({ viewMode: value })}
            >
              <Option value="hourly">{t('chart.hourly')}</Option>
              <Option value="daily">{t('chart.daily')}</Option>
              <Option value="monthly">{t('chart.monthly')}</Option>
            </Select>
          </div>

          {/* 月份选择 - 仅在非月视图时显示 */}
          {viewMode !== 'monthly' && (
            <div>
              <span style={{ marginRight: 8 }}>{t('chart.month')}:</span>
              <Select
                value={month}
                style={{ width: 80 }}
                onChange={(value) => handleVisualizationOptionsChange({ month: value })}
              >
                <Option key={0} value={0}>{t('chart.allYear')}</Option>
                {Array.from({ length: 12 }, (_, i) => i + 1).map(m => (
                  <Option key={m} value={m}>{m}{t('chart.monthUnit')}</Option>
                ))}
              </Select>
            </div>
          )}

          {/* 日期选择 - 仅在小时视图时显示 */}
          {viewMode === 'hourly' && month > 0 && (
            <div>
              <span style={{ marginRight: 8 }}>{t('chart.day')}:</span>
              <Select
                value={day}
                style={{ width: 80 }}
                onChange={(value) => handleVisualizationOptionsChange({ day: value })}
              >
                {Array.from({ length: 31 }, (_, i) => i + 1).map(d => (
                  <Option key={d} value={d}>{d}{t('chart.dayUnit')}</Option>
                ))}
              </Select>
            </div>
          )}
        </div>
      </div>
    );
  };

  // 渲染数据统计
  const renderDataStatistics = () => {
    const selectedData = getSelectedModulesData();
    if (!selectedData) return null;

    const { pvGeneration, pvBenefit } = selectedData;

    // 计算选中组件的总功率
    const selectedModulesList = project.pvModules.filter(module =>
      selectedModules.includes(module.id)
    );
    const totalPower = selectedModulesList.reduce(
      (sum, module) => sum + module.power * module.quantity, 0
    ) / 1000; // 转换为kW

    // 计算每kW的发电量和收益
    const generationPerKW = totalPower > 0 ? pvGeneration / totalPower : 0;
    const benefitPerKW = totalPower > 0 ? pvBenefit / totalPower : 0;

    // 使用统一的投资成本计算工具
    const investmentBreakdown = calculateSelectedModulesInvestment(project, selectedModules);
    const pvReturnRate = investmentBreakdown.totalInvestment > 0 ?
      (pvBenefit / investmentBreakdown.totalInvestment) * 100 : 0;

    console.log('PVAnalysisTab - 选中组件投资明细:', investmentBreakdown);
    console.log('PVAnalysisTab - 光伏年收益率:', pvReturnRate, '%');

    return (
      <Card title={t('analysis.pvDataStatistics')}>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.annualPVGeneration')}
              tooltip={t('analysis.tooltips.annualPVGeneration')}
              value={pvGeneration}
              precision={1}
              suffix="kWh"
            />
          </Col>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.annualPVBenefit')}
              tooltip={t('analysis.tooltips.annualPVBenefit')}
              value={pvBenefit}
              precision={1}
              prefix="JPY "
            />
          </Col>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.generationPerKW')}
              tooltip={t('analysis.tooltips.generationPerKW')}
              value={generationPerKW}
              precision={1}
              suffix="kWh/kW"
            />
          </Col>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.benefitPerKW')}
              tooltip={t('analysis.tooltips.benefitPerKW')}
              value={benefitPerKW}
              precision={1}
              prefix="JPY "
              suffix="/kW"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.pvReturnRate')}
              tooltip={t('analysis.tooltips.pvReturnRate')}
              value={pvReturnRate}
              precision={1}
              suffix="%"
            />
          </Col>
        </Row>
      </Card>
    );
  };

  return (
    <div>
      {renderModuleList()}

      {project.pvModules.length > 0 ? (
        <>
          {renderDataStatistics()}
          <Divider />
          {renderVisualizationOptions()}
          <PVGenerationChart
            data={getSelectedModulesData()}
            options={visualizationOptions}
          />

          {/* 倾角分析图表 */}
          {showTiltAnalysis && (
            <>
              <Divider />
              <PVTiltAnalysisChart
                data={tiltAnalysisData}
                loading={tiltAnalysisLoading}
                error={tiltAnalysisError}
                progress={analysisProgress}
                progressMessage={analysisMessage}
              />
            </>
          )}
        </>
      ) : (
        <Empty
          description={t('pvModules.noModules')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )}

      {/* 添加/编辑组件模态框 */}
      <PVModuleModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSave={handleSaveModule}
        editingModule={editingModule}
      />

      {/* 删除确认对话框 */}
      <Modal
        title={t('pvModules.confirmDelete')}
        open={deleteConfirmVisible}
        onOk={confirmDeleteModule}
        onCancel={() => setDeleteConfirmVisible(false)}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
      >
        <p>{t('pvModules.deleteConfirmMessage')}</p>
      </Modal>

      {/* 倾角分析进度对话框 */}
      <ProgressDialog
        visible={progressDialogVisible}
        title={t('analysis.pvTiltAnalysis')}
        progress={analysisProgress}
        message={analysisMessage}
        onConfirm={confirmTiltAnalysis}
        onCancel={cancelTiltAnalysis}
        confirmDisabled={analysisStarted || tiltAnalysisLoading}
      />
    </div>
  );
};

export default PVAnalysisTab;
