import { message } from 'antd';
import {
  ProjectData,
  ProjectHourlyData,
  ProjectAnalysisResults,
  ProjectDailyData,
  ProjectMonthlyData,
  ProjectYearlyData,
  createEmptyHourlyDataArray
} from '../types/projectData';
import { IrradianceData, ElectricityPrice, PriceRule, SeasonalPricePolicy } from '../types/database';
import { getIrradianceDetail } from './irradianceService';
import { getElectricityPriceDetail } from './electricityPriceService';
import { dataSyncManager } from './dataSyncManager';
import { special } from '../config/settings';
import { validateAnalysisResults } from '../utils/dataValidator';

/**
 * 计算项目分析结果
 * @param project 项目数据
 * @returns 分析结果
 */
export const analyzeProject = async (project: ProjectData): Promise<ProjectData> => {
  try {
    console.log('开始分析项目:', project.id);

    // 1. 获取必要的输入数据
    const irradianceData = await getIrradianceDetail(project.irradianceDataId);
    if (!irradianceData) {
      throw new Error('无法获取光照数据');
    }

    const electricityPrice = await getElectricityPriceDetail(project.electricityPriceId);
    if (!electricityPrice) {
      throw new Error('无法获取电价政策');
    }

    // 2. 初始化分析结果
    const hourlyData = createEmptyHourlyDataArray();

    // 3. 计算小时数据
    await calculateHourlyData(project, hourlyData, irradianceData, electricityPrice);

    // 4. 计算日数据和月数据
    const dailyData = calculateDailyData(hourlyData);
    const monthlyData = calculateMonthlyData(hourlyData);
    const yearlyData = calculateYearlyData(hourlyData, project);

    // 5. 组装分析结果
    const analysisResults: ProjectAnalysisResults = {
      hourlyData,
      dailyData,
      monthlyData,
      yearlyData,
      analysisCompleted: true,
      analysisDate: new Date().toISOString()
    };

    // 6. 验证分析结果数据一致性
    const updatedProject: ProjectData = {
      ...project,
      status: 'completed',
      analysisResults,
      updatedAt: new Date().toISOString()
    };

    const validationResult = validateAnalysisResults(updatedProject);
    if (!validationResult.isValid) {
      console.error('数据验证失败:', validationResult.errors);
      console.warn('数据验证警告:', validationResult.warnings);
      // 不阻止保存，但记录错误
    } else {
      console.log('数据验证通过');
      if (validationResult.warnings.length > 0) {
        console.warn('数据验证警告:', validationResult.warnings);
      }
    }

    // 7. 保存更新后的项目数据
    console.log('保存分析结果到项目数据...');
    const syncResult = await dataSyncManager.syncImmediately(updatedProject.id, updatedProject);

    if (!syncResult.success) {
      console.error('项目分析结果同步失败:', syncResult.error);
      throw new Error('保存项目分析结果失败: ' + syncResult.error);
    }

    console.log('项目分析结果已保存并同步:', syncResult.data!.id);
    console.log('项目分析完成:', updatedProject.id);
    return syncResult.data!;
  } catch (error) {
    console.error('项目分析失败:', error);
    message.error('项目分析失败: ' + (error as Error).message);
    throw error;
  }
};

/**
 * 计算小时数据
 * @param project 项目数据
 * @param hourlyData 小时数据数组
 * @param irradianceData 光照数据
 * @param electricityPrice 电价政策
 */
/**
 * 将朝向字符串转换为与正南的夹角（度）
 * @param orientation 朝向字符串
 * @returns 与正南的夹角，东为负，西为正
 */
const orientationToAngle = (orientation: string): number => {
  // 将字符串朝向转换为角度
  switch (orientation.toLowerCase()) {
    case 'south': return 0; // 正南
    case 'southeast': return -45; // 东南
    case 'east': return -90; // 正东
    case 'northeast': return -135; // 东北
    case 'north': return 180; // 正北
    case 'northwest': return 135; // 西北
    case 'west': return 90; // 正西
    case 'southwest': return 45; // 西南
    default:
      // 如果是数字字符串，直接解析
      const angle = parseFloat(orientation);
      return isNaN(angle) ? 0 : angle;
  }
};

/**
 * 将角度转换为弧度
 * @param degrees 角度
 * @returns 弧度
 */
const degreesToRadians = (degrees: number): number => {
  return degrees * Math.PI / 180;
};

/**
 * 获取特定时间点的电价
 * @param hour 小时 (0-23)
 * @param day 日期 (1-31)
 * @param month 月份 (1-12)
 * @param electricityPrice 电价政策
 * @returns 电价和上网电价
 */
const getHourlyPrice = (
  hour: number,
  day: number,
  month: number,
  electricityPrice: ElectricityPrice
): { price: number, gridFeedInPrice: number } => {
  try {
    // 默认价格
    let price = special.projects.analysis.defaultPrice;
    let gridFeedInPrice = special.projects.analysis.defaultGridFeedInPrice;

    // 时间格式化
    const timeStr = `${hour.toString().padStart(2, '0')}:00`;

    // 根据电价政策类型获取价格
    if (electricityPrice.policyType === 'fixed') {
      // 固定电价
      const rule = findMatchingRule(timeStr, electricityPrice.rules);
      if (rule) {
        price = rule.price;
        gridFeedInPrice = rule.gridFeedInPrice || price * special.projects.analysis.gridExportPriceRatio;
      }
    } else {
      // 季节性电价
      const seasonPolicy = findMatchingSeason(month, electricityPrice.seasonalPolicies);
      if (seasonPolicy) {
        const rule = findMatchingRule(timeStr, seasonPolicy.rules);
        if (rule) {
          price = rule.price;
          gridFeedInPrice = rule.gridFeedInPrice || price * special.projects.analysis.gridExportPriceRatio;
        }
      }
    }

    return { price, gridFeedInPrice };
  } catch (error) {
    console.error('获取电价时出错:', error);
    return {
      price: special.projects.analysis.defaultPrice,
      gridFeedInPrice: special.projects.analysis.defaultGridFeedInPrice
    };
  }
};

/**
 * 查找匹配的时间规则
 * @param timeStr 时间字符串 (HH:MM)
 * @param rules 电价规则数组
 * @returns 匹配的规则或undefined
 */
const findMatchingRule = (timeStr: string, rules: PriceRule[]): PriceRule | undefined => {
  return rules.find(rule => {
    const start = rule.startTime;
    const end = rule.endTime;

    // 处理跨天的情况
    if (start < end) {
      return timeStr >= start && timeStr < end;
    } else {
      return timeStr >= start || timeStr < end;
    }
  });
};

/**
 * 查找匹配的季节
 * @param month 月份 (1-12)
 * @param seasons 季节性电价政策数组
 * @returns 匹配的季节政策或undefined
 */
const findMatchingSeason = (month: number, seasons: SeasonalPricePolicy[]): SeasonalPricePolicy | undefined => {
  return seasons.find(season => season.months.includes(month));
};



const calculateHourlyData = async (
  project: ProjectData,
  hourlyData: ProjectHourlyData[],
  irradianceData: IrradianceData,
  electricityPrice: ElectricityPrice
): Promise<void> => {
  // 计算储能系统总容量 (kWh)
  const storageCapacity = project.energyStorage.reduce((sum, storage) =>
    sum + storage.capacity * storage.quantity, 0);

  // 计算储能系统总功率 (kW)
  const storagePower = project.energyStorage.reduce((sum, storage) =>
    sum + storage.power * storage.quantity, 0);

  // 计算系统效率，使用配置文件中的默认值
  const inverterEfficiency = project.inverters.length > 0
    ? project.inverters.reduce((sum, inverter) => sum + inverter.efficiency * inverter.quantity, 0) /
      project.inverters.reduce((sum, inverter) => sum + inverter.quantity, 0) / 100
    : special.projects.analysis.defaultEfficiency.inverter;

  // 计算储能系统效率
  const storageEfficiency = project.energyStorage.length > 0
    ? project.energyStorage.reduce((sum, storage) => sum + storage.efficiency * storage.quantity, 0) /
      project.energyStorage.reduce((sum, storage) => sum + storage.quantity, 0) / 100
    : special.projects.analysis.defaultEfficiency.storage;

  // 计算储能充放电效率的平方根，用于分别应用于充电和放电过程
  const storageEfficiencyRoot = Math.sqrt(storageEfficiency);

  // 处理每个小时数据点
  for (let i = 0; i < hourlyData.length; i++) {
    const hour = hourlyData[i];

    // 光照数据使用1-24小时制，而项目数据使用0-23小时制
    // 需要将项目数据的小时值+1来匹配光照数据，或者将光照数据的小时值-1来匹配项目数据
    // 这里选择将光照数据的小时值转换为0-23小时制
    const irradianceHour = irradianceData.data.find(
      d => d.month === hour.month && d.day === hour.day && (d.hour === hour.hour + 1 || (hour.hour === 23 && d.hour === 24))
    );

    if (!irradianceHour) continue;

    // 1. 计算每个光伏组件的发电量和总发电量 (kWh)
    let totalPvGeneration = 0;
    const pvGenerationByModule: { [moduleId: string]: number } = {};

    // 遍历每个光伏组件
    for (const module of project.pvModules) {
      // 计算组件效率（百分比转为小数）
      const efficiency = module.efficiency / 100;

      // 获取安装角度和朝向角（与正南的夹角）
      const installAngle = module.angle; // 安装角度
      const orientationAngle = orientationToAngle(module.orientation); // 朝向角

      // 获取太阳高度角和方位角
      const sunHeight = irradianceHour.sunHeight; // 太阳高度角 hs
      const sunAngle = irradianceHour.sunAngle; // 太阳方位角 Az

      // 计算辐射量
      // 漫射辐射
      const diffuseRadiation = irradianceHour.diffuseHorizontalIrradiance;

      // 直射辐射计算
      // 将角度转换为弧度进行三角函数计算
      const sunHeightRad = degreesToRadians(sunHeight);
      const installAngleRad = degreesToRadians(installAngle);
      const sunAngleRad = degreesToRadians(sunAngle);
      const orientationAngleRad = degreesToRadians(orientationAngle);

      // 计算直射辐射贡献
      let directRadiation = 0;
      if (sunHeight > 0) { // 只在太阳高于地平线时计算
        // 计算太阳光线与光伏板法线之间的夹角余弦值
        const cosTheta = Math.sin(sunHeightRad) * Math.cos(installAngleRad) +
                        Math.cos(sunHeightRad) * Math.sin(installAngleRad) * Math.cos(sunAngleRad - orientationAngleRad);

        // 只在光线照射到光伏板正面时计算
        if (cosTheta > 0) {
          directRadiation = irradianceHour.directNormalIrradiance * cosTheta;
        }
      }

      // 总辐射量 (W/m²)
      const totalRadiation = diffuseRadiation + Math.max(0, directRadiation);

      // 计算该组件的发电量 (kWh)
      const moduleArea = module.area * module.quantity; // 总面积 (m²)
      const moduleGeneration = (totalRadiation * moduleArea * efficiency * inverterEfficiency) / 1000;

      // 保存该组件的发电量
      pvGenerationByModule[module.id] = parseFloat(moduleGeneration.toFixed(3));

      // 累加到总发电量
      totalPvGeneration += moduleGeneration;
    }

    // 更新小时数据的光伏发电量
    hour.pvGeneration = pvGenerationByModule;

    // 2. 获取用电量 (kWh)
    const electricityConsumption = getHourlyElectricityConsumption(project, hour.month, hour.day, hour.hour);
    hour.electricityConsumption = parseFloat(electricityConsumption.toFixed(3));

    // 3. 计算电网交互和储能充放电
    let pvSurplus = totalPvGeneration - electricityConsumption;

    // 初始化储能状态
    let storageCharge = 0;
    let storageCapacityNow;

    // 只有在1月1日的第0小时时，storageCapacityNow才设置为0
    // 其他日期的第0小时，使用前一天23点的值
    if (hour.month === 1 && hour.day === 1 && hour.hour === 0) {
      // 1月1日0点，设置为0
      storageCapacityNow = 0;
    } else if (i > 0) {
      // 其他时间点，使用前一小时的值
      storageCapacityNow = hourlyData[i-1].storageCapacity;
    } else {
      // 安全处理，如果i=0但不是1月1日0点（不应该发生）
      storageCapacityNow = 0;
    }

    // 如果有剩余光伏发电，优先考虑充电
    if (pvSurplus > 0 && storageCapacity > 0) {
      // 计算可充电量 - 使用储能效率的平方根
      const maxCharge = Math.min(
        pvSurplus,
        storagePower, // 受储能功率限制
        (storageCapacity - storageCapacityNow) / storageEfficiencyRoot // 受剩余容量限制，使用平方根效率
      );

      storageCharge = maxCharge;
      pvSurplus -= maxCharge;
      // 光伏发电已经考虑了逆变器效率，所以这里不需要再次考虑
      storageCapacityNow += maxCharge * storageEfficiencyRoot; // 充电时使用平方根效率
    } else if (pvSurplus < 0 && storageCapacityNow > 0) {
      // 如果光伏发电不足，考虑放电
      const deficit = -pvSurplus;
      // 计算可放电量 - 使用储能效率的平方根和逆变器效率
      const maxDischarge = Math.min(
        deficit,
        storagePower, // 受储能功率限制
        storageCapacityNow * storageEfficiencyRoot // 受当前容量和效率限制，使用平方根效率
      );

      storageCharge = -maxDischarge; // 负值表示放电
      // 放电时考虑逆变器效率，实际可用电量 = 放电量 * 平方根效率 * 逆变器效率
      pvSurplus += maxDischarge * storageEfficiencyRoot * inverterEfficiency;
      storageCapacityNow -= maxDischarge / storageEfficiencyRoot; // 放电时使用平方根效率
    }

    // 计算电网交互
    let gridExport = 0;
    let gridImport = 0;

    if (pvSurplus > 0) {
      // 有剩余电量上网
      gridExport = pvSurplus;
    } else if (pvSurplus < 0) {
      // 需要从电网购电
      gridImport = -pvSurplus;
    }

    // 获取当前小时的电价
    const { price, gridFeedInPrice } = getHourlyPrice(hour.hour, hour.day, hour.month, electricityPrice);

    // 更新小时数据
    hour.storageCharge = parseFloat(storageCharge.toFixed(3));
    hour.storageCapacity = parseFloat(storageCapacityNow.toFixed(3));
    hour.gridExport = parseFloat(gridExport.toFixed(3));
    hour.gridImport = parseFloat(gridImport.toFixed(3));
    hour.electricityPrice = parseFloat(price.toFixed(3));
    hour.gridFeedInPrice = parseFloat(gridFeedInPrice.toFixed(3));
  }
};



/**
 * 获取指定时间的用电量
 * @param project 项目数据
 * @param month 月份 (1-12)
 * @param day 日期 (1-31)
 * @param hour 小时 (0-23)
 * @returns 用电量 (kWh)
 */
const getHourlyElectricityConsumption = (
  project: ProjectData,
  month: number,
  day: number,
  hour: number
): number => {
  let electricityConsumption = 0;

  // 根据用电数据类型获取对应的用电量
  switch (project.electricityUsage.type) {
    case 'sameEveryday':
      // 每天相同模式
      const hourData = project.electricityUsage.data.find(d => d.hour === hour);
      electricityConsumption = hourData ? hourData.value : 0;
      break;
    case 'sameEveryWeek':
      // 按星期几设置
      // 计算当前日期是星期几 (0-6, 0表示星期日)
      const date = new Date(2023, month - 1, day); // 使用2023年作为基准年
      const dayOfWeek = date.getDay();
      const weekHourData = project.electricityUsage.data.find(
        d => d.week === dayOfWeek && d.hour === hour
      );
      electricityConsumption = weekHourData ? weekHourData.value : 0;
      break;
    case 'monthlyDifferent':
      // 按月份设置
      const monthHourData = project.electricityUsage.data.find(
        d => d.month === month && d.hour === hour
      );
      electricityConsumption = monthHourData ? monthHourData.value : 0;
      break;
    case 'dailyDifferent':
      // 每天不同
      const dailyHourData = project.electricityUsage.data.find(
        d => d.month === month && d.day === day && d.hour === hour
      );
      electricityConsumption = dailyHourData ? dailyHourData.value : 0;
      break;
    default:
      electricityConsumption = 0;
  }

  return electricityConsumption;
};

/**
 * 计算日数据
 * @param hourlyData 小时数据数组
 * @returns 日数据数组
 */
const calculateDailyData = (hourlyData: ProjectHourlyData[]): ProjectDailyData[] => {
  const dailyData: ProjectDailyData[] = [];

  // 按日期分组
  const dailyGroups: { [key: string]: ProjectHourlyData[] } = {};

  hourlyData.forEach(hourData => {
    const key = `${hourData.month}-${hourData.day}`;
    if (!dailyGroups[key]) {
      dailyGroups[key] = [];
    }
    dailyGroups[key].push(hourData);
  });

  // 计算每日数据
  Object.entries(dailyGroups).forEach(([key, hours]) => {
    if (hours.length === 0) return;

    const [month, day] = key.split('-').map(Number);

    // 初始化日数据
    const dailyItem: ProjectDailyData = {
      month,
      day,
      pvGeneration: 0,
      storageCharge: 0,
      storageDischarge: 0,
      electricityConsumption: 0,
      gridExport: 0,
      gridImport: 0,
      gridExportIncome: 0,
      pvBenefit: 0,
      storageBenefit: 0,
      totalBenefit: 0
    };

    // 累加小时数据
    hours.forEach(hour => {
      // 累加光伏发电量
      Object.values(hour.pvGeneration).forEach(gen => {
        dailyItem.pvGeneration += Number(gen) || 0;
      });

      // 累加储能充放电量
      if (hour.storageCharge > 0) {
        dailyItem.storageCharge += hour.storageCharge;
      } else if (hour.storageCharge < 0) {
        dailyItem.storageDischarge += Math.abs(hour.storageCharge);
      }

      // 累加用电量和电网交互
      dailyItem.electricityConsumption += hour.electricityConsumption;
      dailyItem.gridExport += hour.gridExport;
      dailyItem.gridImport += hour.gridImport;

      // 计算经济效益
      dailyItem.gridExportIncome += hour.gridExport * hour.gridFeedInPrice;
    });

    // 计算光伏收益和储能收益 - 使用与年度计算相同的方法
    let pvBenefit = 0;
    let storageBenefit = 0;

    // 遍历该日的小时数据，使用实际电价计算
    hours.forEach(hour => {
      // 直接使用小时数据中的电价，确保与StorageAnalysisChart一致
      const price = hour.electricityPrice;
      const gridFeedInPrice = hour.gridFeedInPrice;

      // 计算总发电量
      const totalHourPvGeneration = Object.values(hour.pvGeneration || {}).reduce((sum, val) => sum + (val as number || 0), 0);

      // 计算该小时的光伏收益（与年度计算方法一致）
      let hourlyPvBenefit = 0;
      if (totalHourPvGeneration <= hour.electricityConsumption) {
        // 全部自用
        hourlyPvBenefit = totalHourPvGeneration * price;
      } else {
        // 部分自用，部分上网
        hourlyPvBenefit = hour.electricityConsumption * price +
                        (totalHourPvGeneration - hour.electricityConsumption) * gridFeedInPrice;
      }
      pvBenefit += hourlyPvBenefit;

      // 计算储能收益 - 使用正确的储能收益计算方法
      let hourlyBenefit = 0;
      const GRID_FEED_IN_PRICE = 15; // 固定上网电价 15日元/度
      if (hour.storageCharge > 0) {
        // 充电时：机会成本 = -固定上网电价 × 充电量
        hourlyBenefit = -GRID_FEED_IN_PRICE * hour.storageCharge;
      } else if (hour.storageCharge < 0) {
        // 放电时：替代收益 = 实际购电价格 × 放电量
        hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
      }
      storageBenefit += hourlyBenefit;
    });

    dailyItem.pvBenefit = pvBenefit;
    dailyItem.storageBenefit = storageBenefit;

    // 计算总收益
    dailyItem.totalBenefit = dailyItem.pvBenefit + dailyItem.storageBenefit;

    // 保留3位小数
    Object.keys(dailyItem).forEach(key => {
      const k = key as keyof ProjectDailyData;
      if (typeof dailyItem[k] === 'number' && key !== 'month' && key !== 'day') {
        dailyItem[k] = parseFloat((dailyItem[k] as number).toFixed(3));
      }
    });

    dailyData.push(dailyItem);
  });

  return dailyData;
};

/**
 * 计算月数据
 * @param hourlyData 小时数据数组
 * @returns 月数据数组
 */
const calculateMonthlyData = (hourlyData: ProjectHourlyData[]): ProjectMonthlyData[] => {
  const monthlyData: ProjectMonthlyData[] = [];

  // 按月份分组
  const monthlyGroups: { [key: string]: ProjectHourlyData[] } = {};

  hourlyData.forEach(hourData => {
    const key = `${hourData.month}`;
    if (!monthlyGroups[key]) {
      monthlyGroups[key] = [];
    }
    monthlyGroups[key].push(hourData);
  });

  // 计算每月数据
  Object.entries(monthlyGroups).forEach(([key, hours]) => {
    if (hours.length === 0) return;

    const month = parseInt(key);

    // 初始化月数据
    const monthlyItem: ProjectMonthlyData = {
      month,
      pvGeneration: 0,
      storageCharge: 0,
      storageDischarge: 0,
      electricityConsumption: 0,
      gridExport: 0,
      gridImport: 0,
      gridExportIncome: 0,
      pvBenefit: 0,
      storageBenefit: 0,
      totalBenefit: 0
    };

    // 累加小时数据
    hours.forEach(hour => {
      // 累加光伏发电量
      Object.values(hour.pvGeneration).forEach(gen => {
        monthlyItem.pvGeneration += Number(gen) || 0;
      });

      // 累加储能充放电量
      if (hour.storageCharge > 0) {
        monthlyItem.storageCharge += hour.storageCharge;
      } else if (hour.storageCharge < 0) {
        monthlyItem.storageDischarge += Math.abs(hour.storageCharge);
      }

      // 累加用电量和电网交互
      monthlyItem.electricityConsumption += hour.electricityConsumption;
      monthlyItem.gridExport += hour.gridExport;
      monthlyItem.gridImport += hour.gridImport;

      // 计算经济效益
      monthlyItem.gridExportIncome += hour.gridExport * hour.gridFeedInPrice;
    });

    // 计算光伏收益和储能收益 - 使用与年度计算相同的方法
    let pvBenefit = 0;
    let storageBenefit = 0;

    // 遍历该月的小时数据，使用实际电价计算
    hours.forEach(hour => {
      // 直接使用小时数据中的电价，确保与StorageAnalysisChart一致
      const price = hour.electricityPrice;
      const gridFeedInPrice = hour.gridFeedInPrice;

      // 计算总发电量
      const totalHourPvGeneration = Object.values(hour.pvGeneration || {}).reduce((sum, val) => sum + (val as number || 0), 0);

      // 计算该小时的光伏收益（与年度计算方法一致）
      let hourlyPvBenefit = 0;
      if (totalHourPvGeneration <= hour.electricityConsumption) {
        // 全部自用
        hourlyPvBenefit = totalHourPvGeneration * price;
      } else {
        // 部分自用，部分上网
        hourlyPvBenefit = hour.electricityConsumption * price +
                        (totalHourPvGeneration - hour.electricityConsumption) * gridFeedInPrice;
      }
      pvBenefit += hourlyPvBenefit;

      // 计算储能收益 - 使用正确的储能收益计算方法
      let hourlyBenefit = 0;
      const GRID_FEED_IN_PRICE = 15; // 固定上网电价 15日元/度
      if (hour.storageCharge > 0) {
        // 充电时：机会成本 = -固定上网电价 × 充电量
        hourlyBenefit = -GRID_FEED_IN_PRICE * hour.storageCharge;
      } else if (hour.storageCharge < 0) {
        // 放电时：替代收益 = 实际购电价格 × 放电量
        hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
      }
      storageBenefit += hourlyBenefit;
    });

    monthlyItem.pvBenefit = pvBenefit;
    monthlyItem.storageBenefit = storageBenefit;

    // 计算总收益
    monthlyItem.totalBenefit = monthlyItem.pvBenefit + monthlyItem.storageBenefit;

    // 保留3位小数
    Object.keys(monthlyItem).forEach(key => {
      const k = key as keyof ProjectMonthlyData;
      if (typeof monthlyItem[k] === 'number' && key !== 'month') {
        monthlyItem[k] = parseFloat((monthlyItem[k] as number).toFixed(3));
      }
    });

    monthlyData.push(monthlyItem);
  });

  return monthlyData;
};

/**
 * 计算年数据
 * @param hourlyData 小时数据数组
 * @param project 项目数据
 * @returns 年数据
 */
const calculateYearlyData = (hourlyData: ProjectHourlyData[], project: ProjectData): ProjectYearlyData => {
  // 初始化年数据
  const yearlyData: ProjectYearlyData = {
    pvGeneration: 0,
    storageCharge: 0,
    storageDischarge: 0,
    electricityConsumption: 0,
    gridExport: 0,
    gridImport: 0,
    gridExportIncome: 0,
    pvBenefit: 0,
    storageBenefit: 0,
    annualElectricityCost: 0,
    renewableEnergyBenefit: 0,
    totalBenefit: 0,
    roi: 0,
    paybackPeriod: 0
  };

  // 累加小时数据
  hourlyData.forEach(hour => {
    // 累加光伏发电量
    Object.values(hour.pvGeneration).forEach(gen => {
      yearlyData.pvGeneration += Number(gen) || 0;
    });

    // 累加储能充放电量
    if (hour.storageCharge > 0) {
      yearlyData.storageCharge += hour.storageCharge;
    } else if (hour.storageCharge < 0) {
      yearlyData.storageDischarge += Math.abs(hour.storageCharge);
    }

    // 累加用电量和电网交互
    yearlyData.electricityConsumption += hour.electricityConsumption;
    yearlyData.gridExport += hour.gridExport;
    yearlyData.gridImport += hour.gridImport;
  });

  // ===== 计算光伏收益 - 使用与ProjectOverviewTab和PVAnalysisTab相同的计算方法 =====
  let pvBenefit = 0;

  // 遍历每个小时数据，计算光伏收益（包含自用和上网部分）
  for (const hour of hourlyData) {
    // 直接使用小时数据中的电价，确保与StorageAnalysisChart一致
    const price = hour.electricityPrice;
    const gridFeedInPrice = hour.gridFeedInPrice;

    // 计算总发电量
    const totalHourPvGeneration = Object.values(hour.pvGeneration || {}).reduce((sum, val) => sum + (val as number || 0), 0);

    // 计算该小时的光伏收益
    let hourlyPvBenefit = 0;

    if (totalHourPvGeneration <= hour.electricityConsumption) {
      // 全部自用
      hourlyPvBenefit = totalHourPvGeneration * price;
    } else {
      // 部分自用，部分上网
      hourlyPvBenefit = hour.electricityConsumption * price +
                      (totalHourPvGeneration - hour.electricityConsumption) * gridFeedInPrice;
    }

    pvBenefit += hourlyPvBenefit;
  }

  // 计算上网收益 - 使用每小时的实际上网电量和上网电价
  const gridExportIncome = hourlyData.reduce((sum, hour) =>
    sum + (hour.gridExport || 0) * (hour.gridFeedInPrice || 17), 0);

  // ===== 计算储能收益 - 修复为正确的计算方法 =====
  let storageBenefit = 0;

  // 储能收益计算原则：光伏和储能收益完全隔离
  // 充电成本：原本可以上网卖15日元/度，现在存入储能，机会成本 = -15日元/度
  // 放电收益：原本需要购电，现在用储能的电，节省成本 = 购电电价/度
  const GRID_FEED_IN_PRICE = 15; // 固定上网电价 15日元/度

  // 遍历所有小时数据，使用正确的储能收益计算逻辑
  for (const hour of hourlyData) {
    // 计算每小时的储能收益
    let hourlyBenefit = 0;
    if (hour.storageCharge > 0) {
      // 充电时：机会成本 = -固定上网电价 × 充电量
      hourlyBenefit = -GRID_FEED_IN_PRICE * hour.storageCharge;
    } else if (hour.storageCharge < 0) {
      // 放电时：替代收益 = 实际购电价格 × 放电量
      hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
    }
    storageBenefit += hourlyBenefit;
  }

  // 计算年购电费用 - 每小时购电量 * 本小时电价的求和
  let annualElectricityCost = 0;
  for (const hour of hourlyData) {
    const price = hour.electricityPrice || 25; // 使用小时数据中的电价，如果没有则使用默认值
    annualElectricityCost += hour.gridImport * price;
  }

  // 更新年度数据
  yearlyData.gridExportIncome = gridExportIncome;
  yearlyData.pvBenefit = pvBenefit;
  yearlyData.storageBenefit = storageBenefit;
  yearlyData.annualElectricityCost = annualElectricityCost;

  // 计算新能源年收益 = 光伏收益 + 储能收益
  yearlyData.renewableEnergyBenefit = pvBenefit + storageBenefit;

  // 计算总收益 = 光伏收益 + 储能收益
  yearlyData.totalBenefit = yearlyData.pvBenefit + yearlyData.storageBenefit;

  // 计算投资回报指标
  // 计算总投资成本
  const pvModulesCost = project.pvModules.reduce((sum, module) => sum + module.price * module.quantity, 0);
  const energyStorageCost = project.energyStorage.reduce((sum, storage) => sum + storage.price * storage.quantity, 0);
  const invertersCost = project.inverters.reduce((sum, inverter) => sum + inverter.price * inverter.quantity, 0);
  const otherInvestmentsCost = project.otherInvestments.reduce((sum, item) => sum + item.price, 0);
  const totalInvestment = pvModulesCost + energyStorageCost + invertersCost + otherInvestmentsCost;

  // 计算ROI和回收期
  yearlyData.roi = totalInvestment > 0 ? (yearlyData.totalBenefit / totalInvestment) * 100 : 0;
  yearlyData.paybackPeriod = yearlyData.totalBenefit > 0 ? totalInvestment / yearlyData.totalBenefit : 0;

  // 保留3位小数
  Object.keys(yearlyData).forEach(key => {
    const k = key as keyof ProjectYearlyData;
    if (typeof yearlyData[k] === 'number') {
      yearlyData[k] = parseFloat((yearlyData[k] as number).toFixed(3));
    }
  });

  return yearlyData;
};

