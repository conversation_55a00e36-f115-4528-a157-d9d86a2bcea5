<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证储能收益修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
            color: #faad14;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f5f5f5;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .log-output {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>验证储能收益修复</h1>
        <p>验证重新分析后的储能收益是否正确，确保所有数据源一致</p>
        
        <div>
            <button onclick="verifyFix()">验证修复结果</button>
            <button onclick="clearLogs()">清除日志</button>
        </div>

        <div id="results"></div>
        <div id="logOutput" class="log-output">等待验证...</div>
    </div>

    <script>
        const projectId = '4d450b50-49e1-48dc-a04e-4cf81aa7268e';
        let logOutput = document.getElementById('logOutput');
        let resultsDiv = document.getElementById('results');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logOutput.textContent += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            logOutput.textContent = '';
            resultsDiv.innerHTML = '';
        }

        async function getProjectData() {
            const response = await fetch(`/api/projects`);
            const result = await response.json();
            
            if (!result.success) {
                throw new Error('获取项目列表失败');
            }

            const project = result.data.items.find(p => p.id === projectId);
            if (!project) {
                throw new Error('项目不存在');
            }

            return project;
        }

        async function verifyFix() {
            try {
                log('=== 开始验证储能收益修复 ===');
                resultsDiv.innerHTML = '';

                // 获取项目数据
                log('获取项目数据...');
                const project = await getProjectData();
                log(`项目名称: ${project.name}`);

                const analysisResults = project.analysisResults;
                if (!analysisResults) {
                    throw new Error('项目没有分析结果');
                }

                // 检查数据完整性
                const hourlyData = analysisResults.hourlyData || [];
                const dailyData = analysisResults.dailyData || [];
                const monthlyData = analysisResults.monthlyData || [];
                const yearlyData = analysisResults.yearlyData;

                log(`小时数据点数: ${hourlyData.length}`);
                log(`日数据点数: ${dailyData.length}`);
                log(`月数据点数: ${monthlyData.length}`);

                if (hourlyData.length === 0) {
                    throw new Error('项目缺少小时数据，请先重新分析项目');
                }

                // 验证储能收益计算
                log('验证储能收益计算...');

                // 1. 使用StorageAnalysisChart的逻辑重新计算储能收益
                let chartCalculatedBenefit = 0;
                for (const hour of hourlyData) {
                    let hourlyBenefit = 0;
                    if (hour.storageCharge > 0) {
                        // 充电时，收益为负（使用上网电价）
                        hourlyBenefit = -hour.gridFeedInPrice * hour.storageCharge;
                    } else if (hour.storageCharge < 0) {
                        // 放电时，收益为正（使用用电价格）
                        hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
                    }
                    chartCalculatedBenefit += hourlyBenefit;
                }

                // 2. 获取年度数据中的储能收益
                const yearlyStorageBenefit = yearlyData?.storageBenefit || 0;

                // 3. 计算日数据总和
                const dailyTotalBenefit = dailyData.reduce((sum, day) => sum + (day.storageBenefit || 0), 0);

                // 4. 计算月数据总和
                const monthlyTotalBenefit = monthlyData.reduce((sum, month) => sum + (month.storageBenefit || 0), 0);

                log(`图表计算逻辑结果: ${chartCalculatedBenefit.toFixed(2)} 日元`);
                log(`年度数据储能收益: ${yearlyStorageBenefit.toFixed(2)} 日元`);
                log(`日数据储能收益总和: ${dailyTotalBenefit.toFixed(2)} 日元`);
                log(`月数据储能收益总和: ${monthlyTotalBenefit.toFixed(2)} 日元`);

                // 数据一致性检查
                const tolerance = 1.0; // 允许1日元的误差
                const yearlyChartDiff = Math.abs(yearlyStorageBenefit - chartCalculatedBenefit);
                const yearlyDailyDiff = Math.abs(yearlyStorageBenefit - dailyTotalBenefit);
                const yearlyMonthlyDiff = Math.abs(yearlyStorageBenefit - monthlyTotalBenefit);
                const dailyMonthlyDiff = Math.abs(dailyTotalBenefit - monthlyTotalBenefit);

                log(`年度数据 vs 图表计算差异: ${yearlyChartDiff.toFixed(2)} 日元`);
                log(`年度数据 vs 日数据差异: ${yearlyDailyDiff.toFixed(2)} 日元`);
                log(`年度数据 vs 月数据差异: ${yearlyMonthlyDiff.toFixed(2)} 日元`);
                log(`日数据 vs 月数据差异: ${dailyMonthlyDiff.toFixed(2)} 日元`);

                // 生成验证结果
                let results = '<h2>验证结果</h2>';
                
                // 数据一致性检查
                const isConsistent = yearlyChartDiff <= tolerance && 
                                   yearlyDailyDiff <= tolerance && 
                                   yearlyMonthlyDiff <= tolerance && 
                                   dailyMonthlyDiff <= tolerance;

                if (isConsistent) {
                    results += '<div class="success">✅ 数据一致性检查通过：所有数据源中的储能收益完全一致</div>';
                } else {
                    results += '<div class="error">❌ 数据一致性检查失败：不同数据源中的储能收益不一致</div>';
                }

                // 检查储能收益是否合理
                const isReasonable = chartCalculatedBenefit > 300000 && chartCalculatedBenefit < 500000;
                if (isReasonable) {
                    results += '<div class="success">✅ 储能收益值合理：在预期范围内（300,000-500,000日元）</div>';
                } else {
                    results += '<div class="warning">⚠️ 储能收益值可能不合理：超出预期范围</div>';
                }

                // 修复状态
                const isFixed = isConsistent && isReasonable;
                if (isFixed) {
                    results += '<div class="success"><h3>🎉 储能收益修复成功！</h3><p>所有数据源一致，收益值合理。</p></div>';
                } else {
                    results += '<div class="error"><h3>❌ 储能收益修复失败</h3><p>仍存在数据不一致或值不合理的问题。</p></div>';
                }

                // 详细数据表
                results += `
                    <h3>详细数据对比</h3>
                    <table class="data-table">
                        <tr><th>数据源</th><th>储能收益（日元）</th><th>与图表计算差异</th><th>状态</th></tr>
                        <tr>
                            <td>图表计算逻辑</td>
                            <td>${chartCalculatedBenefit.toFixed(2)}</td>
                            <td>0.00</td>
                            <td>🎯 基准</td>
                        </tr>
                        <tr>
                            <td>年度数据</td>
                            <td>${yearlyStorageBenefit.toFixed(2)}</td>
                            <td>${yearlyChartDiff.toFixed(2)}</td>
                            <td>${yearlyChartDiff <= tolerance ? '✅' : '❌'}</td>
                        </tr>
                        <tr>
                            <td>日数据总和</td>
                            <td>${dailyTotalBenefit.toFixed(2)}</td>
                            <td>${Math.abs(dailyTotalBenefit - chartCalculatedBenefit).toFixed(2)}</td>
                            <td>${Math.abs(dailyTotalBenefit - chartCalculatedBenefit) <= tolerance ? '✅' : '❌'}</td>
                        </tr>
                        <tr>
                            <td>月数据总和</td>
                            <td>${monthlyTotalBenefit.toFixed(2)}</td>
                            <td>${Math.abs(monthlyTotalBenefit - chartCalculatedBenefit).toFixed(2)}</td>
                            <td>${Math.abs(monthlyTotalBenefit - chartCalculatedBenefit) <= tolerance ? '✅' : '❌'}</td>
                        </tr>
                    </table>
                `;

                // 数据一致性表
                results += `
                    <h3>数据一致性检查</h3>
                    <table class="data-table">
                        <tr><th>比较项</th><th>差异（日元）</th><th>状态</th></tr>
                        <tr>
                            <td>年度数据 vs 图表计算</td>
                            <td>${yearlyChartDiff.toFixed(2)}</td>
                            <td>${yearlyChartDiff <= tolerance ? '✅ 一致' : '❌ 不一致'}</td>
                        </tr>
                        <tr>
                            <td>年度数据 vs 日数据</td>
                            <td>${yearlyDailyDiff.toFixed(2)}</td>
                            <td>${yearlyDailyDiff <= tolerance ? '✅ 一致' : '❌ 不一致'}</td>
                        </tr>
                        <tr>
                            <td>年度数据 vs 月数据</td>
                            <td>${yearlyMonthlyDiff.toFixed(2)}</td>
                            <td>${yearlyMonthlyDiff <= tolerance ? '✅ 一致' : '❌ 不一致'}</td>
                        </tr>
                        <tr>
                            <td>日数据 vs 月数据</td>
                            <td>${dailyMonthlyDiff.toFixed(2)}</td>
                            <td>${dailyMonthlyDiff <= tolerance ? '✅ 一致' : '❌ 不一致'}</td>
                        </tr>
                    </table>
                `;

                resultsDiv.innerHTML = results;
                log('=== 验证完成 ===');

            } catch (error) {
                log(`验证失败: ${error.message}`);
                resultsDiv.innerHTML = `<div class="error">验证失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动运行验证
        window.addEventListener('load', function() {
            log('页面加载完成，等待用户触发验证...');
        });
    </script>
</body>
</html>
