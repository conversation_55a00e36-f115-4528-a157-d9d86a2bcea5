<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正确的项目重新分析</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .log-output {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
            color: #faad14;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>正确的项目重新分析</h1>
        <p>基于真实数据重新分析项目，生成正确的储能收益</p>
        
        <div>
            <button onclick="checkCurrentState()" id="checkBtn">检查当前状态</button>
            <button onclick="properReanalysis()" id="reanalyzeBtn">正确重新分析</button>
            <button onclick="clearLogs()">清除日志</button>
        </div>

        <div id="status"></div>
        <div id="logOutput" class="log-output">等待操作...</div>
    </div>

    <script>
        const projectId = '4d450b50-49e1-48dc-a04e-4cf81aa7268e';
        let logOutput = document.getElementById('logOutput');
        let statusDiv = document.getElementById('status');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logOutput.textContent += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }

        function setStatus(message, type = 'info') {
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLogs() {
            logOutput.textContent = '';
            statusDiv.innerHTML = '';
        }

        async function getProjectData() {
            const response = await fetch(`/api/projects`);
            const result = await response.json();
            
            if (!result.success) {
                throw new Error('获取项目列表失败');
            }

            const project = result.data.items.find(p => p.id === projectId);
            if (!project) {
                throw new Error('项目不存在');
            }

            return project;
        }

        async function checkCurrentState() {
            try {
                log('=== 检查当前项目状态 ===');
                setStatus('正在检查项目状态...', 'warning');

                const project = await getProjectData();
                log(`项目名称: ${project.name}`);
                log(`项目状态: ${project.status}`);

                const analysisResults = project.analysisResults;
                if (!analysisResults) {
                    log('❌ 项目没有分析结果');
                    setStatus('项目没有分析结果，需要重新分析', 'error');
                    return;
                }

                const hourlyData = analysisResults.hourlyData || [];
                const dailyData = analysisResults.dailyData || [];
                const monthlyData = analysisResults.monthlyData || [];
                const yearlyData = analysisResults.yearlyData;

                log(`小时数据点数: ${hourlyData.length}`);
                log(`日数据点数: ${dailyData.length}`);
                log(`月数据点数: ${monthlyData.length}`);

                if (yearlyData) {
                    log(`年度储能收益: ${yearlyData.storageBenefit} 日元`);
                    log(`年度总收益: ${yearlyData.totalBenefit} 日元`);
                    log(`年度ROI: ${yearlyData.roi}%`);
                }

                // 检查项目配置
                log(`光照数据ID: ${project.irradianceDataId}`);
                log(`电价政策ID: ${project.electricityPriceId}`);
                log(`光伏组件数量: ${project.pvModules.length}`);
                log(`储能设备数量: ${project.energyStorage.length}`);
                log(`逆变器数量: ${project.inverters.length}`);

                if (hourlyData.length === 0) {
                    setStatus('❌ 项目缺少小时数据，需要重新分析', 'error');
                } else if (hourlyData.length === 8760) {
                    setStatus('✅ 项目有完整的小时数据', 'success');
                } else {
                    setStatus(`⚠️ 项目小时数据不完整 (${hourlyData.length}/8760)`, 'warning');
                }

            } catch (error) {
                log(`检查失败: ${error.message}`);
                setStatus(`检查失败: ${error.message}`, 'error');
            }
        }

        async function properReanalysis() {
            try {
                const reanalyzeBtn = document.getElementById('reanalyzeBtn');
                reanalyzeBtn.disabled = true;
                reanalyzeBtn.textContent = '分析中...';

                log('=== 开始正确的项目重新分析 ===');
                setStatus('正在重新分析项目，请稍候...', 'warning');

                // 获取项目数据
                log('步骤 1: 获取项目数据...');
                const project = await getProjectData();
                log(`项目: ${project.name}`);

                // 检查必要的数据
                if (!project.irradianceDataId) {
                    throw new Error('项目缺少光照数据ID');
                }
                if (!project.electricityPriceId) {
                    throw new Error('项目缺少电价政策ID');
                }

                // 获取光照数据
                log('步骤 2: 获取光照数据...');
                const irradianceResponse = await fetch(`/api/irradiance/${project.irradianceDataId}`);
                const irradianceResult = await irradianceResponse.json();
                if (!irradianceResult.success) {
                    throw new Error('获取光照数据失败');
                }
                const irradianceData = irradianceResult.data;
                log(`光照数据: ${irradianceData.name}, 数据点数: ${irradianceData.data.length}`);

                // 获取电价数据
                log('步骤 3: 获取电价数据...');
                const priceResponse = await fetch(`/api/electricity-prices/${project.electricityPriceId}`);
                const priceResult = await priceResponse.json();
                if (!priceResult.success) {
                    throw new Error('获取电价数据失败');
                }
                const electricityPrice = priceResult.data;
                log(`电价政策: ${electricityPrice.name}, 类型: ${electricityPrice.policyType}`);

                // 这里我们需要调用前端的分析逻辑
                // 由于我们在HTML页面中，无法直接调用TypeScript模块
                // 我们需要打开项目分析页面并手动点击重新分析按钮

                log('步骤 4: 打开项目分析页面...');
                const analysisUrl = `http://localhost:5173/projects/analysis/${projectId}`;
                log(`请在新打开的页面中点击"重新分析"按钮: ${analysisUrl}`);
                
                // 自动打开链接
                window.open(analysisUrl, '_blank');
                
                setStatus('已打开项目分析页面，请手动点击"重新分析"按钮', 'warning');
                
                // 等待用户操作后检查结果
                log('等待用户在分析页面点击重新分析按钮...');
                log('分析完成后，请回到此页面点击"检查当前状态"按钮验证结果');

            } catch (error) {
                log(`重新分析失败: ${error.message}`);
                setStatus(`重新分析失败: ${error.message}`, 'error');
            } finally {
                const reanalyzeBtn = document.getElementById('reanalyzeBtn');
                reanalyzeBtn.disabled = false;
                reanalyzeBtn.textContent = '正确重新分析';
            }
        }

        // 页面加载时自动检查状态
        window.addEventListener('load', function() {
            log('页面加载完成');
            setTimeout(checkCurrentState, 1000);
        });
    </script>
</body>
</html>
