<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>储能收益数据一致性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .success {
            background-color: #f6ffed;
            border-color: #b7eb8f;
        }
        .error {
            background-color: #fff2f0;
            border-color: #ffccc7;
        }
        .warning {
            background-color: #fffbe6;
            border-color: #ffe58f;
        }
        .log-output {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .status {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>储能收益数据一致性测试</h1>
        <p>此页面用于验证项目中储能收益计算的数据一致性</p>
        
        <div class="test-section">
            <h2>测试控制</h2>
            <button onclick="runConsistencyTest()">运行一致性测试</button>
            <button onclick="clearLogs()">清除日志</button>
            <button onclick="fetchProjectData()">获取项目数据</button>
        </div>

        <div id="testResults" class="test-section" style="display: none;">
            <h2>测试结果</h2>
            <div id="testStatus" class="status"></div>
            <div id="testDetails"></div>
        </div>

        <div id="projectData" class="test-section" style="display: none;">
            <h2>项目数据</h2>
            <div id="projectInfo"></div>
        </div>

        <div class="test-section">
            <h2>控制台日志</h2>
            <div id="logOutput" class="log-output">等待测试运行...</div>
        </div>
    </div>

    <script>
        let logOutput = document.getElementById('logOutput');
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;

        // 重写console方法以捕获日志
        function captureConsole() {
            console.log = function(...args) {
                logOutput.textContent += '[LOG] ' + args.join(' ') + '\n';
                logOutput.scrollTop = logOutput.scrollHeight;
                originalConsoleLog.apply(console, args);
            };

            console.error = function(...args) {
                logOutput.textContent += '[ERROR] ' + args.join(' ') + '\n';
                logOutput.scrollTop = logOutput.scrollHeight;
                originalConsoleError.apply(console, args);
            };

            console.warn = function(...args) {
                logOutput.textContent += '[WARN] ' + args.join(' ') + '\n';
                logOutput.scrollTop = logOutput.scrollHeight;
                originalConsoleWarn.apply(console, args);
            };
        }

        function clearLogs() {
            logOutput.textContent = '';
        }

        async function fetchProjectData() {
            try {
                console.log('开始获取项目数据...');
                const projectId = '4d450b50-49e1-48dc-a04e-4cf81aa7268e';
                const response = await fetch(`/api/projects/${projectId}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const project = await response.json();
                console.log('项目数据获取成功');
                
                // 显示项目基本信息
                const projectInfo = document.getElementById('projectInfo');
                projectInfo.innerHTML = `
                    <h3>项目基本信息</h3>
                    <p><strong>项目名称:</strong> ${project.name}</p>
                    <p><strong>项目ID:</strong> ${project.id}</p>
                    <p><strong>分析状态:</strong> ${project.analysisResults?.analysisCompleted ? '已完成' : '未完成'}</p>
                    <p><strong>小时数据点数:</strong> ${project.analysisResults?.hourlyData?.length || 0}</p>
                    <p><strong>储能设备数量:</strong> ${project.energyStorage?.length || 0}</p>
                `;
                document.getElementById('projectData').style.display = 'block';
                
                return project;
            } catch (error) {
                console.error('获取项目数据失败:', error);
                return null;
            }
        }

        async function runConsistencyTest() {
            console.log('=== 开始储能收益数据一致性测试 ===');
            
            const project = await fetchProjectData();
            if (!project) {
                console.error('无法获取项目数据，测试终止');
                return;
            }

            const hourlyData = project.analysisResults?.hourlyData || [];
            const yearlyData = project.analysisResults?.yearlyData;
            
            if (!yearlyData) {
                console.error('缺少年度数据，测试终止');
                return;
            }

            console.log('项目数据验证通过，开始计算...');

            // 使用与StorageAnalysisChart相同的计算方法
            let calculatedStorageBenefit = 0;
            let chargeHours = 0;
            let dischargeHours = 0;
            let totalChargeAmount = 0;
            let totalDischargeAmount = 0;

            for (const hour of hourlyData) {
                let hourlyBenefit = 0;
                if (hour.storageCharge > 0) {
                    // 充电时，收益为负（使用上网电价）
                    hourlyBenefit = -hour.gridFeedInPrice * hour.storageCharge;
                    chargeHours++;
                    totalChargeAmount += hour.storageCharge;
                } else if (hour.storageCharge < 0) {
                    // 放电时，收益为正（使用用电价格）
                    hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
                    dischargeHours++;
                    totalDischargeAmount += Math.abs(hour.storageCharge);
                }
                calculatedStorageBenefit += hourlyBenefit;
            }

            const yearlyStorageBenefit = yearlyData.storageBenefit;
            const difference = Math.abs(calculatedStorageBenefit - yearlyStorageBenefit);
            const isConsistent = difference <= 1.0;

            console.log('=== 计算结果 ===');
            console.log('重新计算的储能收益:', calculatedStorageBenefit.toFixed(2), '日元');
            console.log('年度数据中的储能收益:', yearlyStorageBenefit.toFixed(2), '日元');
            console.log('差异:', difference.toFixed(2), '日元');
            console.log('充电小时数:', chargeHours);
            console.log('放电小时数:', dischargeHours);
            console.log('总充电量:', totalChargeAmount.toFixed(1), 'kWh');
            console.log('总放电量:', totalDischargeAmount.toFixed(1), 'kWh');

            // 显示测试结果
            const testResults = document.getElementById('testResults');
            const testStatus = document.getElementById('testStatus');
            const testDetails = document.getElementById('testDetails');

            if (isConsistent) {
                testResults.className = 'test-section success';
                testStatus.textContent = '✅ 测试通过：储能收益数据一致';
                console.log('✅ 测试通过：储能收益数据一致');
            } else {
                testResults.className = 'test-section error';
                testStatus.textContent = '❌ 测试失败：储能收益数据不一致';
                console.error('❌ 测试失败：储能收益数据不一致');
            }

            testDetails.innerHTML = `
                <table class="data-table">
                    <tr><th>项目</th><th>值</th></tr>
                    <tr><td>重新计算的储能收益</td><td>¥${calculatedStorageBenefit.toFixed(2)}</td></tr>
                    <tr><td>年度数据中的储能收益</td><td>¥${yearlyStorageBenefit.toFixed(2)}</td></tr>
                    <tr><td>差异</td><td>¥${difference.toFixed(2)}</td></tr>
                    <tr><td>一致性状态</td><td>${isConsistent ? '✅ 一致' : '❌ 不一致'}</td></tr>
                    <tr><td>充电小时数</td><td>${chargeHours}</td></tr>
                    <tr><td>放电小时数</td><td>${dischargeHours}</td></tr>
                    <tr><td>总充电量</td><td>${totalChargeAmount.toFixed(1)} kWh</td></tr>
                    <tr><td>总放电量</td><td>${totalDischargeAmount.toFixed(1)} kWh</td></tr>
                </table>
            `;

            testResults.style.display = 'block';
            console.log('=== 测试完成 ===');
        }

        // 初始化控制台捕获
        captureConsole();
        
        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            console.log('页面加载完成，准备运行测试...');
            setTimeout(runConsistencyTest, 1000);
        });
    </script>
</body>
</html>
