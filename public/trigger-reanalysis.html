<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>触发项目重新分析</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .log-output {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
            color: #faad14;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>触发项目重新分析</h1>
        <p>此页面用于触发项目重新分析，以应用储能收益计算的修复</p>
        
        <div>
            <button onclick="triggerReanalysis()" id="reanalyzeBtn">重新分析项目</button>
            <button onclick="checkStorageBenefit()" id="checkBtn">检查储能收益</button>
            <button onclick="clearLogs()">清除日志</button>
        </div>

        <div id="status"></div>
        <div id="logOutput" class="log-output">等待操作...</div>
    </div>

    <script>
        const projectId = '4d450b50-49e1-48dc-a04e-4cf81aa7268e';
        let logOutput = document.getElementById('logOutput');
        let statusDiv = document.getElementById('status');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logOutput.textContent += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }

        function setStatus(message, type = 'info') {
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLogs() {
            logOutput.textContent = '';
            statusDiv.innerHTML = '';
        }

        async function checkStorageBenefit() {
            try {
                log('开始检查储能收益...');
                setStatus('正在检查储能收益...', 'warning');

                const response = await fetch(`/api/projects`);
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error('获取项目列表失败');
                }

                const project = result.data.items.find(p => p.id === projectId);
                if (!project) {
                    throw new Error('项目不存在');
                }

                const storageBenefit = project.analysisResults?.yearlyData?.storageBenefit;
                log(`当前储能收益: ${storageBenefit} 日元`);

                if (Math.abs(storageBenefit - 96916.541) < 1) {
                    setStatus('❌ 储能收益仍然是错误的值 (96916.541)，需要重新分析', 'error');
                    log('储能收益需要重新分析');
                } else if (Math.abs(storageBenefit - 385799.4) < 1) {
                    setStatus('✅ 储能收益已修复为正确值 (385799.4)', 'success');
                    log('储能收益已修复');
                } else {
                    setStatus(`⚠️ 储能收益为未知值: ${storageBenefit}`, 'warning');
                    log(`储能收益为未知值: ${storageBenefit}`);
                }

            } catch (error) {
                log(`检查储能收益失败: ${error.message}`);
                setStatus(`检查失败: ${error.message}`, 'error');
            }
        }

        async function triggerReanalysis() {
            try {
                const reanalyzeBtn = document.getElementById('reanalyzeBtn');
                const checkBtn = document.getElementById('checkBtn');
                
                reanalyzeBtn.disabled = true;
                checkBtn.disabled = true;
                reanalyzeBtn.textContent = '分析中...';

                log('开始触发项目重新分析...');
                setStatus('正在重新分析项目，请稍候...', 'warning');

                // 首先获取项目数据
                log('获取项目数据...');
                const projectResponse = await fetch(`/api/projects`);
                const projectResult = await projectResponse.json();
                
                if (!projectResult.success) {
                    throw new Error('获取项目数据失败');
                }

                const project = projectResult.data.items.find(p => p.id === projectId);
                if (!project) {
                    throw new Error('项目不存在');
                }

                log(`找到项目: ${project.name}`);

                // 模拟前端重新分析过程
                // 这里我们需要调用前端的分析逻辑
                log('准备重新分析...');
                
                // 由于我们无法直接调用前端的分析函数，我们需要通过其他方式
                // 最简单的方法是直接在浏览器中打开项目分析页面并点击重新分析按钮
                
                const analysisUrl = `http://localhost:5173/projects/analysis/${projectId}`;
                log(`请在浏览器中打开以下链接并点击"重新分析"按钮:`);
                log(analysisUrl);
                
                // 自动打开链接
                window.open(analysisUrl, '_blank');
                
                setStatus('已打开项目分析页面，请手动点击"重新分析"按钮', 'warning');
                
                // 等待一段时间后检查结果
                setTimeout(async () => {
                    log('等待分析完成...');
                    await checkStorageBenefit();
                    
                    reanalyzeBtn.disabled = false;
                    checkBtn.disabled = false;
                    reanalyzeBtn.textContent = '重新分析项目';
                }, 5000);

            } catch (error) {
                log(`重新分析失败: ${error.message}`);
                setStatus(`重新分析失败: ${error.message}`, 'error');
                
                const reanalyzeBtn = document.getElementById('reanalyzeBtn');
                const checkBtn = document.getElementById('checkBtn');
                reanalyzeBtn.disabled = false;
                checkBtn.disabled = false;
                reanalyzeBtn.textContent = '重新分析项目';
            }
        }

        // 页面加载时自动检查储能收益
        window.addEventListener('load', function() {
            log('页面加载完成');
            setTimeout(checkStorageBenefit, 1000);
        });
    </script>
</body>
</html>
